"""
Settings module for Hiel Physics application.

This module contains all application-wide configuration constants and settings
that can be imported and used throughout the application.
"""

import flet as ft
from typing import Dict, List, Tuple
from enum import Enum


# Application Metadata
APP_NAME = "Hiel Physics"
APP_VERSION = "1.0.0"
APP_DESCRIPTION = "Interactive Physics Education Platform for Moroccan High School Students"
APP_AUTHOR = "Hiel Physics Team"
APP_COPYRIGHT = "© 2024 Hiel Physics. All rights reserved."

# Window Configuration
WINDOW_WIDTH = 1200
WINDOW_HEIGHT = 800
WINDOW_MIN_WIDTH = 800
WINDOW_MIN_HEIGHT = 600
WINDOW_TITLE = APP_NAME
WINDOW_RESIZABLE = True
WINDOW_MAXIMIZABLE = True

# Language Settings
class Language(Enum):
    ARABIC = "ar"
    FRENCH = "fr"

DEFAULT_LANGUAGE = Language.ARABIC
SUPPORTED_LANGUAGES = [Language.ARABIC, Language.FRENCH]

# Language Display Names
LANGUAGE_NAMES = {
    Language.ARABIC: "العربية",
    Language.FRENCH: "Français"
}

# Educational Tracks
class EducationalTrack(Enum):
    SCIENCES_MATHS_A = "sciences_maths_a"
    SCIENCES_MATHS_B = "sciences_maths_b"
    SCIENCES_PHYSIQUES = "sciences_physiques"
    SVT = "svt"
    SCIENCES_TECHNIQUES_ELECTRIQUES = "sciences_techniques_electriques"
    SCIENCES_TECHNIQUES_MECANIQUES = "sciences_techniques_mecaniques"
    SCIENCES_ECONOMIQUES = "sciences_economiques"
    LETTRES = "lettres"

# Track Display Names (Arabic/French)
TRACK_NAMES = {
    EducationalTrack.SCIENCES_MATHS_A: {
        Language.ARABIC: "علوم رياضيات أ",
        Language.FRENCH: "Sciences Mathématiques A"
    },
    EducationalTrack.SCIENCES_MATHS_B: {
        Language.ARABIC: "علوم رياضيات ب",
        Language.FRENCH: "Sciences Mathématiques B"
    },
    EducationalTrack.SCIENCES_PHYSIQUES: {
        Language.ARABIC: "علوم فيزيائية",
        Language.FRENCH: "Sciences Physiques"
    },
    EducationalTrack.SVT: {
        Language.ARABIC: "علوم الحياة والأرض",
        Language.FRENCH: "Sciences de la Vie et de la Terre"
    },
    EducationalTrack.SCIENCES_TECHNIQUES_ELECTRIQUES: {
        Language.ARABIC: "علوم تقنية كهربائية",
        Language.FRENCH: "Sciences Techniques Électriques"
    },
    EducationalTrack.SCIENCES_TECHNIQUES_MECANIQUES: {
        Language.ARABIC: "علوم تقنية ميكانيكية",
        Language.FRENCH: "Sciences Techniques Mécaniques"
    },
    EducationalTrack.SCIENCES_ECONOMIQUES: {
        Language.ARABIC: "علوم اقتصادية",
        Language.FRENCH: "Sciences Économiques"
    },
    EducationalTrack.LETTRES: {
        Language.ARABIC: "آداب",
        Language.FRENCH: "Lettres"
    }
}

# Theme Configuration
class ThemeMode(Enum):
    LIGHT = "light"
    DARK = "dark"
    SYSTEM = "system"

DEFAULT_THEME_MODE = ThemeMode.LIGHT

# Color Scheme
PRIMARY_COLOR = ft.Colors.BLUE_700
SECONDARY_COLOR = ft.Colors.TEAL_600
ACCENT_COLOR = ft.Colors.ORANGE_600
ERROR_COLOR = ft.Colors.RED_600
WARNING_COLOR = ft.Colors.AMBER_600
SUCCESS_COLOR = ft.Colors.GREEN_600
INFO_COLOR = ft.Colors.BLUE_600

# Background Colors
BACKGROUND_COLOR_LIGHT = ft.Colors.GREY_50
BACKGROUND_COLOR_DARK = ft.Colors.GREY_900
SURFACE_COLOR_LIGHT = ft.Colors.WHITE
SURFACE_COLOR_DARK = ft.Colors.GREY_800

# Text Colors
TEXT_PRIMARY_LIGHT = ft.Colors.GREY_900
TEXT_PRIMARY_DARK = ft.Colors.WHITE
TEXT_SECONDARY_LIGHT = ft.Colors.GREY_700
TEXT_SECONDARY_DARK = ft.Colors.GREY_300

# Navigation Configuration
NAVIGATION_RAIL_WIDTH = 280
NAVIGATION_RAIL_COLLAPSED_WIDTH = 72
NAVIGATION_RAIL_ANIMATION_DURATION = 300

# Main Navigation Items
class NavigationItem(Enum):
    HOME = "home"
    COURSES = "courses"
    QUIZZES = "quizzes"
    EXERCISES = "exercises"
    EXAMS = "exams"
    SIMULATIONS = "simulations"
    PROGRESS = "progress"
    SETTINGS = "settings"

# Navigation Item Configuration
NAVIGATION_ITEMS = {
    NavigationItem.HOME: {
        "icon": ft.Icons.HOME,
        "icon_selected": ft.Icons.HOME_FILLED,
        "label": {
            Language.ARABIC: "الرئيسية",
            Language.FRENCH: "Accueil"
        }
    },
    NavigationItem.COURSES: {
        "icon": ft.Icons.SCHOOL_OUTLINED,
        "icon_selected": ft.Icons.SCHOOL,
        "label": {
            Language.ARABIC: "الدروس",
            Language.FRENCH: "Cours"
        }
    },
    NavigationItem.QUIZZES: {
        "icon": ft.Icons.QUIZ_OUTLINED,
        "icon_selected": ft.Icons.QUIZ,
        "label": {
            Language.ARABIC: "الاختبارات القصيرة",
            Language.FRENCH: "Quiz"
        }
    },
    NavigationItem.EXERCISES: {
        "icon": ft.Icons.FITNESS_CENTER_OUTLINED,
        "icon_selected": ft.Icons.FITNESS_CENTER,
        "label": {
            Language.ARABIC: "التمارين",
            Language.FRENCH: "Exercices"
        }
    },
    NavigationItem.EXAMS: {
        "icon": ft.Icons.ASSIGNMENT_OUTLINED,
        "icon_selected": ft.Icons.ASSIGNMENT,
        "label": {
            Language.ARABIC: "الامتحانات",
            Language.FRENCH: "Examens"
        }
    },
    NavigationItem.SIMULATIONS: {
        "icon": ft.Icons.SCIENCE_OUTLINED,
        "icon_selected": ft.Icons.SCIENCE,
        "label": {
            Language.ARABIC: "المحاكاة",
            Language.FRENCH: "Simulations"
        }
    },
    NavigationItem.PROGRESS: {
        "icon": ft.Icons.TRENDING_UP_OUTLINED,
        "icon_selected": ft.Icons.TRENDING_UP,
        "label": {
            Language.ARABIC: "التقدم",
            Language.FRENCH: "Progrès"
        }
    },
    NavigationItem.SETTINGS: {
        "icon": ft.Icons.SETTINGS_OUTLINED,
        "icon_selected": ft.Icons.SETTINGS,
        "label": {
            Language.ARABIC: "الإعدادات",
            Language.FRENCH: "Paramètres"
        }
    }
}

# Content Areas
CONTENT_PADDING = 20
CARD_ELEVATION = 2
CARD_BORDER_RADIUS = 12
BUTTON_BORDER_RADIUS = 8

# Typography
FONT_SIZE_DISPLAY_LARGE = 57
FONT_SIZE_DISPLAY_MEDIUM = 45
FONT_SIZE_DISPLAY_SMALL = 36
FONT_SIZE_HEADLINE_LARGE = 32
FONT_SIZE_HEADLINE_MEDIUM = 28
FONT_SIZE_HEADLINE_SMALL = 24
FONT_SIZE_TITLE_LARGE = 22
FONT_SIZE_TITLE_MEDIUM = 16
FONT_SIZE_TITLE_SMALL = 14
FONT_SIZE_BODY_LARGE = 16
FONT_SIZE_BODY_MEDIUM = 14
FONT_SIZE_BODY_SMALL = 12
FONT_SIZE_LABEL_LARGE = 14
FONT_SIZE_LABEL_MEDIUM = 12
FONT_SIZE_LABEL_SMALL = 11

# Animation Settings
ANIMATION_DURATION_SHORT = 150
ANIMATION_DURATION_MEDIUM = 300
ANIMATION_DURATION_LONG = 500

# Physics Simulation Settings
SIMULATION_FPS = 60
SIMULATION_QUALITY = "high"  # low, medium, high
MANIM_QUALITY = "medium_quality"  # low_quality, medium_quality, high_quality

# File Paths
ASSETS_DIR = "assets"
IMAGES_DIR = f"{ASSETS_DIR}/images"
ICONS_DIR = f"{ASSETS_DIR}/icons"
LOCALES_DIR = "locales"
CONFIG_DIR = "config"

# Database/Storage Settings
USER_DATA_FILE = "user_data.json"
SETTINGS_FILE = "app_settings.json"
PROGRESS_FILE = "user_progress.json"

# API Configuration (for future use)
API_BASE_URL = ""
API_TIMEOUT = 30
API_RETRY_ATTEMPTS = 3

# Validation Rules
MIN_PASSWORD_LENGTH = 8
MAX_USERNAME_LENGTH = 50
MAX_EMAIL_LENGTH = 254

# Physics Constants (for educational content)
PHYSICS_CONSTANTS = {
    "speed_of_light": 299792458,  # m/s
    "planck_constant": 6.62607015e-34,  # J⋅s
    "elementary_charge": 1.602176634e-19,  # C
    "avogadro_number": 6.02214076e23,  # mol⁻¹
    "boltzmann_constant": 1.380649e-23,  # J/K
    "gravitational_constant": 6.67430e-11,  # m³/kg⋅s²
    "earth_gravity": 9.81,  # m/s²
}

# Educational Levels
class EducationalLevel(Enum):
    FIRST_YEAR = "1bac"  # 1ère Bac
    SECOND_YEAR = "2bac"  # 2ème Bac (Terminal)

LEVEL_NAMES = {
    EducationalLevel.FIRST_YEAR: {
        Language.ARABIC: "السنة الأولى بكالوريا",
        Language.FRENCH: "Première Baccalauréat"
    },
    EducationalLevel.SECOND_YEAR: {
        Language.ARABIC: "السنة الثانية بكالوريا",
        Language.FRENCH: "Deuxième Baccalauréat"
    }
}

# Content Types
class ContentType(Enum):
    THEORY = "theory"
    EXERCISE = "exercise"
    QUIZ = "quiz"
    EXAM = "exam"
    SIMULATION = "simulation"
    VIDEO = "video"
    DOCUMENT = "document"

# Difficulty Levels
class DifficultyLevel(Enum):
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"

DIFFICULTY_NAMES = {
    DifficultyLevel.BEGINNER: {
        Language.ARABIC: "مبتدئ",
        Language.FRENCH: "Débutant"
    },
    DifficultyLevel.INTERMEDIATE: {
        Language.ARABIC: "متوسط",
        Language.FRENCH: "Intermédiaire"
    },
    DifficultyLevel.ADVANCED: {
        Language.ARABIC: "متقدم",
        Language.FRENCH: "Avancé"
    }
}

# Default User Preferences
DEFAULT_USER_PREFERENCES = {
    "language": DEFAULT_LANGUAGE.value,
    "theme_mode": DEFAULT_THEME_MODE.value,
    "educational_track": None,
    "educational_level": None,
    "navigation_collapsed": False,
    "show_animations": True,
    "auto_save": True,
    "notification_enabled": True,
    "sound_enabled": True
}

# Error Messages
ERROR_MESSAGES = {
    "network_error": {
        Language.ARABIC: "خطأ في الاتصال بالشبكة",
        Language.FRENCH: "Erreur de connexion réseau"
    },
    "file_not_found": {
        Language.ARABIC: "الملف غير موجود",
        Language.FRENCH: "Fichier introuvable"
    },
    "invalid_input": {
        Language.ARABIC: "مدخل غير صحيح",
        Language.FRENCH: "Entrée invalide"
    },
    "permission_denied": {
        Language.ARABIC: "تم رفض الإذن",
        Language.FRENCH: "Permission refusée"
    }
}

# Success Messages
SUCCESS_MESSAGES = {
    "data_saved": {
        Language.ARABIC: "تم حفظ البيانات بنجاح",
        Language.FRENCH: "Données sauvegardées avec succès"
    },
    "settings_updated": {
        Language.ARABIC: "تم تحديث الإعدادات",
        Language.FRENCH: "Paramètres mis à jour"
    },
    "exercise_completed": {
        Language.ARABIC: "تم إكمال التمرين",
        Language.FRENCH: "Exercice terminé"
    }
}

# Onboarding Configuration
ONBOARDING_STEPS = ["language", "track"]
ONBOARDING_CARD_WIDTH = 400
ONBOARDING_CARD_HEIGHT = 200
ONBOARDING_ANIMATION_DURATION = 300

# I18n Configuration
I18N_CATALOG_DIR = "locales"
I18N_DEFAULT_LANGUAGE = Language.ARABIC
I18N_FALLBACK_LANGUAGE = Language.ARABIC
I18N_SUPPORTED_LANGUAGES = [Language.ARABIC, Language.FRENCH]

# RTL Support
RTL_LANGUAGES = [Language.ARABIC]

# Application URLs (for future web integration)
WEBSITE_URL = "https://hielphysics.ma"
SUPPORT_URL = "https://support.hielphysics.ma"
DOCUMENTATION_URL = "https://docs.hielphysics.ma"