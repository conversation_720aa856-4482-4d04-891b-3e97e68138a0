"""
Core routing system for the Hiel Physics Flet application.

This module provides a Router class that manages navigation between different views,
handles route changes, and maintains the navigation stack for the physics education app.
"""

import flet as ft
from typing import Dict, Callable, Optional, List, Any
from dataclasses import dataclass

from services.app_state import AppState
from services.i18n import I18nService
from services.theme_service import ThemeService
from ui.views.onboarding.language_selection import create_language_selection_view
from ui.views.onboarding.track_selection import create_track_selection_view
from ui.views.home import create_home_view
from ui.views.courses import create_courses_view
from ui.views.quizzes import create_quizzes_view
from ui.views.exercises import create_exercises_view
from ui.views.exams import create_exams_view
from ui.views.simulations import create_simulations_view
from ui.views.settings import create_settings_view
from ui.layout.main_layout import MainLayout
from ui.utils.navigation_utils import get_selected_index, is_navigation_route


@dataclass
class RouteInfo:
    """Information about a route including path, parameters, and metadata."""
    path: str
    params: Dict[str, Any]
    query_params: Dict[str, str]
    
    
class Router:
    """
    Core routing system for the Flet application.
    
    Manages navigation between different views and handles route changes.
    Supports dynamic routes with parameters and maintains navigation history.
    """
    
    def __init__(self, page: ft.Page, i18n_service: I18nService, theme_service: ThemeService):
        """
        Initialize the router with a Flet page.

        Args:
            page: The main Flet page object
            i18n_service: Internationalization service
            theme_service: Theme service
        """
        self.page = page
        self.i18n_service = i18n_service
        self.theme_service = theme_service
        self.routes: Dict[str, Callable] = {}
        self.middleware: List[Callable] = []
        self.navigation_history: List[str] = []
        self.current_route: Optional[RouteInfo] = None
        self.main_layout: Optional[MainLayout] = None

        # Set up route change handler
        self.page.on_route_change = self._handle_route_change
        self.page.on_view_pop = self._handle_view_pop

        # Initialize default routes for main app sections
        self._register_default_routes()
        
    def _register_default_routes(self):
        """Register default routes for main app sections."""
        self.routes.update({
            "/": lambda route_info: self._create_main_view(route_info, create_home_view),
            "/home": lambda route_info: self._create_main_view(route_info, create_home_view),
            "/courses": lambda route_info: self._create_main_view(route_info, create_courses_view),
            "/courses/:track": lambda route_info: self._create_main_view(route_info, create_courses_view),
            "/courses/:track/:subject": lambda route_info: self._create_main_view(route_info, create_courses_view),
            "/quizzes": lambda route_info: self._create_main_view(route_info, create_quizzes_view),
            "/quizzes/:id": lambda route_info: self._create_main_view(route_info, create_quizzes_view),
            "/exercises": lambda route_info: self._create_main_view(route_info, create_exercises_view),
            "/exercises/:id": lambda route_info: self._create_main_view(route_info, create_exercises_view),
            "/exams": lambda route_info: self._create_main_view(route_info, create_exams_view),
            "/exams/:id": lambda route_info: self._create_main_view(route_info, create_exams_view),
            "/simulations": lambda route_info: self._create_main_view(route_info, create_simulations_view),
            "/simulations/:id": lambda route_info: self._create_main_view(route_info, create_simulations_view),
            "/settings": lambda route_info: self._create_settings_view(route_info),
            "/profile": self._placeholder_view,
            # Onboarding routes
            "/onboarding/language": lambda route_info: create_language_selection_view(self.page),
            "/onboarding/track": lambda route_info: create_track_selection_view(self.page),
        })
    
    def register_route(self, pattern: str, view_builder: Callable) -> None:
        """
        Register a route with its corresponding view builder function.
        
        Args:
            pattern: Route pattern (e.g., "/courses/:track")
            view_builder: Function that returns a Flet View object
        """
        self.routes[pattern] = view_builder
    
    def add_middleware(self, middleware_func: Callable) -> None:
        """
        Add middleware function to be executed before route handling.
        
        Args:
            middleware_func: Function to execute before route processing
        """
        self.middleware.append(middleware_func)
    
    def navigate_to(self, route: str, replace: bool = False) -> None:
        """
        Navigate to a specific route.
        
        Args:
            route: Target route path
            replace: Whether to replace current route in history
        """
        if replace and self.navigation_history:
            self.navigation_history[-1] = route
        else:
            self.navigation_history.append(route)
        
        self.page.go(route)
    
    def go_back(self) -> bool:
        """
        Navigate back to the previous route.
        
        Returns:
            True if navigation was successful, False if no history
        """
        if len(self.navigation_history) > 1:
            # Remove current route
            self.navigation_history.pop()
            # Navigate to previous route
            previous_route = self.navigation_history[-1]
            self.page.go(previous_route)
            return True
        return False
    
    def can_go_back(self) -> bool:
        """
        Check if backward navigation is possible.
        
        Returns:
            True if there's navigation history, False otherwise
        """
        return len(self.navigation_history) > 1
    
    def get_current_route(self) -> Optional[RouteInfo]:
        """
        Get information about the current route.
        
        Returns:
            RouteInfo object with current route details
        """
        return self.current_route
    
    def clear_history(self) -> None:
        """Clear the navigation history."""
        self.navigation_history.clear()
    
    def _handle_route_change(self, e) -> None:
        """
        Handle route changes and update the view stack.

        Args:
            e: Route change event
        """
        # Check if user needs onboarding before processing route
        if self._should_redirect_to_onboarding():
            return

        # Execute middleware
        for middleware in self.middleware:
            try:
                middleware(self.page.route)
            except Exception as ex:
                print(f"Middleware error: {ex}")

        # Parse the current route
        route_info = self._parse_route(self.page.route)
        self.current_route = route_info

        # Find matching route pattern and build view
        view = self._build_view_for_route(route_info)

        # Update the view stack
        self.page.views.clear()
        self.page.views.append(view)

        # Update main layout navigation if it exists
        if self.main_layout and is_navigation_route(route_info.path):
            self.main_layout.update_selected_item(route_info.path)

        self.page.update()

    def _should_redirect_to_onboarding(self) -> bool:
        """
        Check if user should be redirected to onboarding flow.

        Returns:
            True if redirect happened, False otherwise
        """
        app_state = AppState()
        current_route = self.page.route

        # Skip redirect if already in onboarding
        if current_route.startswith("/onboarding"):
            return False

        # Check if user needs onboarding
        if app_state.app_settings.first_run or not app_state.get_educational_track():
            self.page.go("/onboarding/language")
            return True

        return False
    
    def _handle_view_pop(self, e) -> None:
        """
        Handle view pop events (back button).
        
        Args:
            e: View pop event
        """
        self.page.views.pop()
        top_view = self.page.views[-1] if self.page.views else None
        if top_view:
            self.page.go(top_view.route)
    
    def _parse_route(self, route: str) -> RouteInfo:
        """
        Parse a route string into RouteInfo object.
        
        Args:
            route: Route string to parse
            
        Returns:
            RouteInfo object with parsed route data
        """
        # Split route and query parameters
        if "?" in route:
            path, query_string = route.split("?", 1)
            query_params = self._parse_query_params(query_string)
        else:
            path = route
            query_params = {}
        
        # Extract route parameters using TemplateRoute
        params = {}
        for pattern in self.routes.keys():
            template_route = ft.TemplateRoute(route)
            if template_route.match(pattern):
                # Extract parameters from the matched route
                for key in dir(template_route):
                    if not key.startswith('_') and key not in ['match', 'route']:
                        value = getattr(template_route, key)
                        if value is not None:
                            params[key] = value
                break
        
        return RouteInfo(path=path, params=params, query_params=query_params)
    
    def _parse_query_params(self, query_string: str) -> Dict[str, str]:
        """
        Parse query parameters from query string.
        
        Args:
            query_string: Query string to parse
            
        Returns:
            Dictionary of query parameters
        """
        params = {}
        if query_string:
            for param in query_string.split("&"):
                if "=" in param:
                    key, value = param.split("=", 1)
                    params[key] = value
                else:
                    params[param] = ""
        return params

    def _create_main_view(self, route_info: RouteInfo, content_factory: Callable) -> ft.View:
        """Create a main view with layout wrapper.

        Args:
            route_info: Route information
            content_factory: Function to create content view

        Returns:
            Flet View with main layout
        """
        # Create content view
        if content_factory == create_home_view:
            content_view = content_factory(route_info, self)
        else:
            content_view = content_factory(self.i18n_service)

        # Create or update main layout
        if not self.main_layout:
            self.main_layout = MainLayout(
                page=self.page,
                i18n_service=self.i18n_service,
                theme_service=self.theme_service,
                on_navigation_click=self._handle_navigation_click
            )

        # Set content in main layout
        self.main_layout.set_content_view(content_view)

        # Create view with main layout
        return ft.View(
            route=route_info.path,
            controls=[self.main_layout],
            padding=0,
            spacing=0
        )

    def _create_settings_view(self, route_info: RouteInfo) -> ft.View:
        """Create settings view with special handling.

        Args:
            route_info: Route information

        Returns:
            Settings view with main layout
        """
        from services.app_state import AppState
        app_state = AppState()

        content_view = create_settings_view(
            self.i18n_service,
            self.theme_service,
            app_state
        )

        # Create or update main layout
        if not self.main_layout:
            self.main_layout = MainLayout(
                page=self.page,
                i18n_service=self.i18n_service,
                theme_service=self.theme_service,
                on_navigation_click=self._handle_navigation_click
            )

        # Set content in main layout
        self.main_layout.set_content_view(content_view)

        # Create view with main layout
        return ft.View(
            route=route_info.path,
            controls=[self.main_layout],
            padding=0,
            spacing=0
        )

    def _handle_navigation_click(self, route: str) -> None:
        """Handle navigation clicks from main layout.

        Args:
            route: Target route
        """
        self.navigate_to(route)

    def _build_view_for_route(self, route_info: RouteInfo) -> ft.View:
        """
        Build a view for the given route.
        
        Args:
            route_info: Information about the route
            
        Returns:
            Flet View object for the route
        """
        # Find matching route pattern
        for pattern, view_builder in self.routes.items():
            template_route = ft.TemplateRoute(route_info.path)
            if template_route.match(pattern):
                try:
                    return view_builder(route_info)
                except Exception as ex:
                    print(f"Error building view for route {route_info.path}: {ex}")
                    return self._error_view(route_info, str(ex))
        
        # No matching route found
        return self._not_found_view(route_info)
    
    def _placeholder_view(self, route_info: RouteInfo) -> ft.View:
        """
        Create a placeholder view for routes that haven't been implemented yet.
        
        Args:
            route_info: Information about the route
            
        Returns:
            Placeholder Flet View
        """
        return ft.View(
            route=route_info.path,
            controls=[
                ft.AppBar(
                    title=ft.Text("Hiel Physics"),
                    bgcolor=ft.Colors.SURFACE,
                    automatically_imply_leading=self.can_go_back(),
                ),
                ft.Container(
                    content=ft.Column([
                        ft.Text(
                            f"Route: {route_info.path}",
                            size=24,
                            weight=ft.FontWeight.BOLD,
                        ),
                        ft.Text(
                            "This view is under development",
                            size=16,
                            color=ft.Colors.GREY_600,
                        ),
                        ft.Divider(),
                        ft.Text("Route Parameters:", weight=ft.FontWeight.BOLD),
                        ft.Text(str(route_info.params) if route_info.params else "None"),
                        ft.Text("Query Parameters:", weight=ft.FontWeight.BOLD),
                        ft.Text(str(route_info.query_params) if route_info.query_params else "None"),
                        ft.ElevatedButton(
                            "Go Home",
                            on_click=lambda _: self.navigate_to("/"),
                        ),
                        ft.ElevatedButton(
                            "Go Back",
                            on_click=lambda _: self.go_back(),
                            disabled=not self.can_go_back(),
                        ),
                    ]),
                    padding=20,
                ),
            ],
        )
    
    def _not_found_view(self, route_info: RouteInfo) -> ft.View:
        """
        Create a 404 not found view.
        
        Args:
            route_info: Information about the route
            
        Returns:
            404 Flet View
        """
        return ft.View(
            route="/404",
            controls=[
                ft.AppBar(
                    title=ft.Text("Page Not Found"),
                    bgcolor=ft.Colors.ERROR_CONTAINER,
                ),
                ft.Container(
                    content=ft.Column([
                        ft.Icon(
                            ft.Icons.ERROR_OUTLINE,
                            size=64,
                            color=ft.Colors.ERROR,
                        ),
                        ft.Text(
                            "404 - Page Not Found",
                            size=24,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.ERROR,
                        ),
                        ft.Text(
                            f"The route '{route_info.path}' was not found.",
                            size=16,
                        ),
                        ft.ElevatedButton(
                            "Go Home",
                            on_click=lambda _: self.navigate_to("/"),
                        ),
                    ],
                    alignment=ft.MainAxisAlignment.CENTER,
                    horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                    ),
                    padding=20,
                    alignment=ft.alignment.center,
                ),
            ],
        )
    
    def _error_view(self, route_info: RouteInfo, error_message: str) -> ft.View:
        """
        Create an error view for when view building fails.
        
        Args:
            route_info: Information about the route
            error_message: Error message to display
            
        Returns:
            Error Flet View
        """
        return ft.View(
            route="/error",
            controls=[
                ft.AppBar(
                    title=ft.Text("Error"),
                    bgcolor=ft.Colors.ERROR_CONTAINER,
                ),
                ft.Container(
                    content=ft.Column([
                        ft.Icon(
                            ft.Icons.WARNING,
                            size=64,
                            color=ft.Colors.AMBER,
                        ),
                        ft.Text(
                            "An error occurred",
                            size=24,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.ERROR,
                        ),
                        ft.Text(
                            f"Route: {route_info.path}",
                            size=16,
                        ),
                        ft.Text(
                            f"Error: {error_message}",
                            size=14,
                            color=ft.Colors.ERROR,
                        ),
                        ft.ElevatedButton(
                            "Go Home",
                            on_click=lambda _: self.navigate_to("/"),
                        ),
                    ],
                    alignment=ft.MainAxisAlignment.CENTER,
                    horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                    ),
                    padding=20,
                    alignment=ft.alignment.center,
                ),
            ],
        )


# Convenience functions for common navigation patterns
def create_app_router(page: ft.Page, i18n_service: I18nService, theme_service: ThemeService) -> Router:
    """
    Create and configure a router for the Hiel Physics app.

    Args:
        page: The main Flet page object
        i18n_service: Internationalization service
        theme_service: Theme service

    Returns:
        Configured Router instance
    """
    router = Router(page, i18n_service, theme_service)

    # Add any app-specific middleware here
    def auth_middleware(route: str):
        """Example middleware for authentication checks."""
        # This would check if user is authenticated for protected routes
        pass

    # router.add_middleware(auth_middleware)

    return router