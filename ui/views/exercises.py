"""
Exercises view - placeholder implementation for demonstration.
"""

import flet as ft
from services.i18n import I18nService


def create_exercises_view(i18n_service: I18nService) -> ft.Control:
    """Create the exercises view.
    
    Args:
        i18n_service: Internationalization service
        
    Returns:
        Exercises view control
    """
    return ft.Container(
        content=ft.Column([
            # Page title
            ft.Text(
                i18n_service.t("navigation.exercises"),
                size=32,
                weight=ft.FontWeight.BOLD
            ),
            
            # Page description
            ft.Text(
                i18n_service.t("exercises.description"),
                size=16,
                color=ft.colors.ON_SURFACE_VARIANT
            ),
            
            ft.Container(height=24),  # Spacing
            
            # Exercise categories
            ft.Row([
                ft.Card(
                    content=ft.Container(
                        content=ft.Column([
                            ft.Icon(ft.icons.CALCULATE, size=48, color=ft.colors.PRIMARY),
                            ft.Text(
                                i18n_service.t("exercises.problem_solving"),
                                size=18,
                                weight=ft.FontWeight.BOLD,
                                text_align=ft.TextAlign.CENTER
                            ),
                            ft.Text(
                                i18n_service.t("exercises.problem_solving_description"),
                                size=14,
                                color=ft.colors.ON_SURFACE_VARIANT,
                                text_align=ft.TextAlign.CENTER
                            ),
                            ft.ElevatedButton(
                                text=i18n_service.t("exercises.practice"),
                                icon=ft.icons.PLAY_ARROW
                            )
                        ], 
                        horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                        spacing=12
                        ),
                        padding=20,
                        width=280
                    ),
                    elevation=2
                ),
                
                ft.Card(
                    content=ft.Container(
                        content=ft.Column([
                            ft.Icon(ft.icons.SCIENCE, size=48, color=ft.colors.SECONDARY),
                            ft.Text(
                                i18n_service.t("exercises.lab_simulations"),
                                size=18,
                                weight=ft.FontWeight.BOLD,
                                text_align=ft.TextAlign.CENTER
                            ),
                            ft.Text(
                                i18n_service.t("exercises.lab_simulations_description"),
                                size=14,
                                color=ft.colors.ON_SURFACE_VARIANT,
                                text_align=ft.TextAlign.CENTER
                            ),
                            ft.ElevatedButton(
                                text=i18n_service.t("exercises.practice"),
                                icon=ft.icons.PLAY_ARROW
                            )
                        ], 
                        horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                        spacing=12
                        ),
                        padding=20,
                        width=280
                    ),
                    elevation=2
                )
            ], 
            wrap=True,
            spacing=16,
            run_spacing=16
            ),
            
            ft.Container(height=32),  # Spacing
            
            # Progress tracking
            ft.Text(
                i18n_service.t("exercises.progress"),
                size=24,
                weight=ft.FontWeight.BOLD
            ),
            
            ft.Container(height=16),  # Spacing
            
            ft.Row([
                ft.Card(
                    content=ft.Container(
                        content=ft.Column([
                            ft.Text("156", size=28, weight=ft.FontWeight.BOLD, color=ft.colors.PRIMARY),
                            ft.Text(i18n_service.t("exercises.completed"), size=14)
                        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                        padding=16,
                        width=140
                    )
                ),
                ft.Card(
                    content=ft.Container(
                        content=ft.Column([
                            ft.Text("89%", size=28, weight=ft.FontWeight.BOLD, color=ft.colors.SECONDARY),
                            ft.Text(i18n_service.t("exercises.accuracy"), size=14)
                        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                        padding=16,
                        width=140
                    )
                ),
                ft.Card(
                    content=ft.Container(
                        content=ft.Column([
                            ft.Text("42", size=28, weight=ft.FontWeight.BOLD, color=ft.colors.TERTIARY),
                            ft.Text(i18n_service.t("exercises.streak"), size=14)
                        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                        padding=16,
                        width=140
                    )
                )
            ], spacing=16),
            
            ft.Container(height=32),  # Spacing
            
            # Recent exercises
            ft.Text(
                i18n_service.t("exercises.recent"),
                size=24,
                weight=ft.FontWeight.BOLD
            ),
            
            ft.Container(height=16),  # Spacing
            
            # Exercise list
            ft.Column([
                ft.ListTile(
                    leading=ft.Icon(ft.icons.ASSIGNMENT, color=ft.colors.PRIMARY),
                    title=ft.Text(i18n_service.t("exercises.kinematics_problems")),
                    subtitle=ft.Text(i18n_service.t("exercises.difficulty_medium")),
                    trailing=ft.Row([
                        ft.Icon(ft.icons.STAR, color=ft.colors.AMBER, size=16),
                        ft.Text("4.5"),
                        ft.Icon(ft.icons.ARROW_FORWARD_IOS)
                    ], tight=True)
                ),
                ft.ListTile(
                    leading=ft.Icon(ft.icons.ASSIGNMENT, color=ft.colors.SECONDARY),
                    title=ft.Text(i18n_service.t("exercises.energy_conservation")),
                    subtitle=ft.Text(i18n_service.t("exercises.difficulty_hard")),
                    trailing=ft.Row([
                        ft.Icon(ft.icons.STAR, color=ft.colors.AMBER, size=16),
                        ft.Text("4.8"),
                        ft.Icon(ft.icons.ARROW_FORWARD_IOS)
                    ], tight=True)
                ),
                ft.ListTile(
                    leading=ft.Icon(ft.icons.ASSIGNMENT, color=ft.colors.TERTIARY),
                    title=ft.Text(i18n_service.t("exercises.wave_interference")),
                    subtitle=ft.Text(i18n_service.t("exercises.difficulty_easy")),
                    trailing=ft.Row([
                        ft.Icon(ft.icons.STAR, color=ft.colors.AMBER, size=16),
                        ft.Text("4.2"),
                        ft.Icon(ft.icons.ARROW_FORWARD_IOS)
                    ], tight=True)
                )
            ])
        ], 
        spacing=8,
        scroll=ft.ScrollMode.AUTO
        ),
        padding=ft.padding.all(24),
        expand=True
    )
