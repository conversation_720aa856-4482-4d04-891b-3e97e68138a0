"""
Settings view for configuring app preferences.
"""

import flet as ft
from services.i18n import I18nService
from services.app_state import AppState
from ui.components.theme_toggle import ThemeToggle
from ui.components.language_selector import LanguageSelector
from ui.components.track_selector import TrackSelector
from services.theme_service import ThemeService


def create_settings_view(
    i18n_service: I18nService, 
    theme_service: ThemeService,
    app_state: AppState
) -> ft.Control:
    """Create the settings view.
    
    Args:
        i18n_service: Internationalization service
        theme_service: Theme service
        app_state: Application state service
        
    Returns:
        Settings view control
    """
    return ft.Container(
        content=ft.Column([
            # Page title
            ft.Text(
                i18n_service.t("navigation.settings"),
                size=32,
                weight=ft.FontWeight.BOLD
            ),
            
            # Page description
            ft.Text(
                i18n_service.t("settings.description"),
                size=16,
                color=ft.colors.ON_SURFACE_VARIANT
            ),
            
            ft.Container(height=24),  # Spacing
            
            # Appearance section
            ft.Card(
                content=ft.Container(
                    content=ft.Column([
                        ft.Text(
                            i18n_service.t("settings.appearance"),
                            size=20,
                            weight=ft.FontWeight.BOLD
                        ),
                        
                        ft.Container(height=16),  # Spacing
                        
                        # Theme setting
                        ft.Row([
                            ft.Column([
                                ft.Text(
                                    i18n_service.t("settings.theme"),
                                    size=16,
                                    weight=ft.FontWeight.W500
                                ),
                                ft.Text(
                                    i18n_service.t("settings.theme_description"),
                                    size=14,
                                    color=ft.colors.ON_SURFACE_VARIANT
                                )
                            ], expand=True),
                            ThemeToggle(
                                theme_service=theme_service,
                                i18n_service=i18n_service
                            )
                        ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN)
                    ]),
                    padding=24
                ),
                elevation=1
            ),
            
            ft.Container(height=16),  # Spacing
            
            # Language & Region section
            ft.Card(
                content=ft.Container(
                    content=ft.Column([
                        ft.Text(
                            i18n_service.t("settings.language_region"),
                            size=20,
                            weight=ft.FontWeight.BOLD
                        ),
                        
                        ft.Container(height=16),  # Spacing
                        
                        # Language setting
                        ft.Row([
                            ft.Column([
                                ft.Text(
                                    i18n_service.t("settings.language"),
                                    size=16,
                                    weight=ft.FontWeight.W500
                                ),
                                ft.Text(
                                    i18n_service.t("settings.language_description"),
                                    size=14,
                                    color=ft.colors.ON_SURFACE_VARIANT
                                )
                            ], expand=True),
                            LanguageSelector(
                                i18n_service=i18n_service,
                                app_state=app_state
                            )
                        ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                        
                        ft.Container(height=16),  # Spacing
                        
                        # Educational track setting
                        ft.Row([
                            ft.Column([
                                ft.Text(
                                    i18n_service.t("settings.educational_track"),
                                    size=16,
                                    weight=ft.FontWeight.W500
                                ),
                                ft.Text(
                                    i18n_service.t("settings.track_description"),
                                    size=14,
                                    color=ft.colors.ON_SURFACE_VARIANT
                                )
                            ], expand=True),
                            TrackSelector(
                                i18n_service=i18n_service,
                                app_state=app_state
                            )
                        ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN)
                    ]),
                    padding=24
                ),
                elevation=1
            ),
            
            ft.Container(height=16),  # Spacing
            
            # Notifications section
            ft.Card(
                content=ft.Container(
                    content=ft.Column([
                        ft.Text(
                            i18n_service.t("settings.notifications"),
                            size=20,
                            weight=ft.FontWeight.BOLD
                        ),
                        
                        ft.Container(height=16),  # Spacing
                        
                        # Push notifications
                        ft.Row([
                            ft.Column([
                                ft.Text(
                                    i18n_service.t("settings.push_notifications"),
                                    size=16,
                                    weight=ft.FontWeight.W500
                                ),
                                ft.Text(
                                    i18n_service.t("settings.push_description"),
                                    size=14,
                                    color=ft.colors.ON_SURFACE_VARIANT
                                )
                            ], expand=True),
                            ft.Switch(value=True)
                        ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                        
                        ft.Container(height=16),  # Spacing
                        
                        # Email notifications
                        ft.Row([
                            ft.Column([
                                ft.Text(
                                    i18n_service.t("settings.email_notifications"),
                                    size=16,
                                    weight=ft.FontWeight.W500
                                ),
                                ft.Text(
                                    i18n_service.t("settings.email_description"),
                                    size=14,
                                    color=ft.colors.ON_SURFACE_VARIANT
                                )
                            ], expand=True),
                            ft.Switch(value=False)
                        ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN)
                    ]),
                    padding=24
                ),
                elevation=1
            ),
            
            ft.Container(height=16),  # Spacing
            
            # Privacy & Security section
            ft.Card(
                content=ft.Container(
                    content=ft.Column([
                        ft.Text(
                            i18n_service.t("settings.privacy_security"),
                            size=20,
                            weight=ft.FontWeight.BOLD
                        ),
                        
                        ft.Container(height=16),  # Spacing
                        
                        # Data collection
                        ft.Row([
                            ft.Column([
                                ft.Text(
                                    i18n_service.t("settings.data_collection"),
                                    size=16,
                                    weight=ft.FontWeight.W500
                                ),
                                ft.Text(
                                    i18n_service.t("settings.data_description"),
                                    size=14,
                                    color=ft.colors.ON_SURFACE_VARIANT
                                )
                            ], expand=True),
                            ft.Switch(value=True)
                        ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                        
                        ft.Container(height=16),  # Spacing
                        
                        # Clear data button
                        ft.Row([
                            ft.Column([
                                ft.Text(
                                    i18n_service.t("settings.clear_data"),
                                    size=16,
                                    weight=ft.FontWeight.W500
                                ),
                                ft.Text(
                                    i18n_service.t("settings.clear_description"),
                                    size=14,
                                    color=ft.colors.ON_SURFACE_VARIANT
                                )
                            ], expand=True),
                            ft.OutlinedButton(
                                text=i18n_service.t("settings.clear"),
                                icon=ft.icons.DELETE_OUTLINE,
                                on_click=lambda e: print("Clear data clicked")
                            )
                        ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN)
                    ]),
                    padding=24
                ),
                elevation=1
            ),
            
            ft.Container(height=32),  # Spacing
            
            # About section
            ft.Card(
                content=ft.Container(
                    content=ft.Column([
                        ft.Text(
                            i18n_service.t("settings.about"),
                            size=20,
                            weight=ft.FontWeight.BOLD
                        ),
                        
                        ft.Container(height=16),  # Spacing
                        
                        ft.Row([
                            ft.Text(i18n_service.t("settings.version"), size=16),
                            ft.Text("1.0.0", size=16, weight=ft.FontWeight.W500)
                        ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                        
                        ft.Container(height=8),  # Spacing
                        
                        ft.Row([
                            ft.TextButton(
                                text=i18n_service.t("settings.privacy_policy"),
                                on_click=lambda e: print("Privacy policy clicked")
                            ),
                            ft.TextButton(
                                text=i18n_service.t("settings.terms_of_service"),
                                on_click=lambda e: print("Terms clicked")
                            )
                        ])
                    ]),
                    padding=24
                ),
                elevation=1
            )
        ], 
        spacing=8,
        scroll=ft.ScrollMode.AUTO
        ),
        padding=ft.padding.all(24),
        expand=True
    )
