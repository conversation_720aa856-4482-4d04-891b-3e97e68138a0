"""
Home view for the Hiel Physics application.

This module provides the main landing page view that serves as the entry point
for the physics education app. It includes basic layout structure and content
integrated with the main layout system.
"""

import flet as ft
from typing import Optional, Dict, Any
from dataclasses import dataclass
from services.i18n import I18nService


@dataclass
class RouteInfo:
    """Information about a route including path, parameters, and metadata."""
    path: str
    params: Dict[str, Any]
    query_params: Dict[str, str]


def create_home_view(route_info: RouteInfo, router=None) -> ft.Control:
    """
    Create the home view content.

    Args:
        route_info: Route information from the router
        router: Router instance for navigation

    Returns:
        Home view content control
    """
    # Get i18n service from router if available
    i18n_service = getattr(router, 'i18n_service', None)
    if not i18n_service:
        from services.i18n import get_i18n_service
        i18n_service = get_i18n_service()

    return _build_home_content(i18n_service, router)


def _build_home_content(i18n_service: I18nService, router=None) -> ft.Control:
    """Build the main home content."""

    # Welcome section
    welcome_section = ft.Container(
        content=ft.Column([
            ft.Text(
                i18n_service.t("home.welcome_message"),
                size=24,
                weight=ft.FontWeight.BOLD,
                text_align=ft.TextAlign.CENTER,
            ),
            ft.Container(height=16),  # Spacing
            ft.Text(
                i18n_service.t("home.description"),
                size=16,
                text_align=ft.TextAlign.CENTER,
                color=ft.colors.ON_SURFACE_VARIANT
            ),
        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
        padding=24,
        margin=ft.margin.only(bottom=24),
    )

    # Quick access cards
    quick_access_cards = _build_quick_access_cards(i18n_service, router)

    # Stats section
    stats_section = _build_stats_section(i18n_service)

    # Recent activity section
    recent_activity = _build_recent_activity(i18n_service)

    # Main content
    return ft.Container(
        content=ft.Column([
            welcome_section,
            quick_access_cards,
            stats_section,
            recent_activity,
        ],
        spacing=8,
        scroll=ft.ScrollMode.AUTO
        ),
        padding=ft.padding.all(24),
        expand=True
    )


def _build_quick_access_cards(i18n_service: I18nService, router=None) -> ft.Container:
    """Build quick access cards for main features."""

    # Define quick access items
    quick_items = [
        {
            "icon": ft.icons.BOOK,
            "title_key": "navigation.courses",
            "description_key": "home.courses_description",
            "route": "/courses",
            "color": ft.colors.PRIMARY,
        },
        {
            "icon": ft.icons.QUIZ,
            "title_key": "navigation.quizzes",
            "description_key": "home.quizzes_description",
            "route": "/quizzes",
            "color": ft.colors.SECONDARY,
        },
        {
            "icon": ft.icons.FITNESS_CENTER,
            "title_key": "navigation.exercises",
            "description_key": "home.exercises_description",
            "route": "/exercises",
            "color": ft.colors.TERTIARY,
        },
        {
            "icon": ft.icons.SCIENCE,
            "title_key": "navigation.simulations",
            "description_key": "home.simulations_description",
            "route": "/simulations",
            "color": ft.colors.PRIMARY,
        },
    ]

    # Create cards
    cards = []
    for item in quick_items:
        card = _create_quick_access_card(item, i18n_service, router)
        cards.append(card)

    return ft.Container(
        content=ft.Column([
            ft.Text(
                i18n_service.t("home.quick_access"),
                size=20,
                weight=ft.FontWeight.BOLD
            ),
            ft.Container(height=16),  # Spacing
            ft.Row(
                cards,
                wrap=True,
                spacing=16,
                run_spacing=16,
                alignment=ft.MainAxisAlignment.CENTER
            )
        ]),
        margin=ft.margin.only(bottom=32),
    )


def _create_quick_access_card(item: dict, i18n_service: I18nService, router=None) -> ft.Container:
    """Create a quick access card."""
    def on_card_click(e):
        if router:
            router.navigate_to(item["route"])
        else:
            print(f"Navigate to: {item['route']}")

    return ft.Card(
        content=ft.Container(
            content=ft.Column([
                ft.Icon(
                    item["icon"],
                    size=48,
                    color=item["color"],
                ),
                ft.Text(
                    i18n_service.t(item["title_key"]),
                    size=16,
                    weight=ft.FontWeight.BOLD,
                    text_align=ft.TextAlign.CENTER,
                ),
                ft.Text(
                    i18n_service.t(item["description_key"]),
                    size=14,
                    color=ft.colors.ON_SURFACE_VARIANT,
                    text_align=ft.TextAlign.CENTER,
                    max_lines=2,
                ),
                ft.ElevatedButton(
                    text=i18n_service.t("common.explore"),
                    icon=ft.icons.ARROW_FORWARD,
                    on_click=on_card_click
                )
            ],
            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            spacing=12,
            ),
            width=200,
            padding=20,
        ),
        elevation=2,
        on_click=on_card_click
    )

def _build_stats_section(i18n_service: I18nService) -> ft.Container:
    """Build the stats section."""
    stats = [
        {"label_key": "home.tracks", "value": "4", "icon": ft.icons.SCHOOL},
        {"label_key": "home.courses", "value": "120+", "icon": ft.icons.BOOK},
        {"label_key": "home.exercises", "value": "500+", "icon": ft.icons.FITNESS_CENTER},
        {"label_key": "home.simulations", "value": "25+", "icon": ft.icons.SCIENCE},
    ]

    stat_cards = []
    for stat in stats:
        card = ft.Card(
            content=ft.Container(
                content=ft.Column([
                    ft.Icon(stat["icon"], size=32, color=ft.colors.PRIMARY),
                    ft.Text(
                        stat["value"],
                        size=24,
                        weight=ft.FontWeight.BOLD,
                        color=ft.colors.PRIMARY,
                    ),
                    ft.Text(
                        i18n_service.t(stat["label_key"]),
                        size=14,
                        text_align=ft.TextAlign.CENTER,
                    ),
                ],
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                spacing=8,
                ),
                width=120,
                padding=16,
            ),
            elevation=1
        )
        stat_cards.append(card)

    return ft.Container(
        content=ft.Column([
            ft.Text(
                i18n_service.t("home.statistics"),
                size=20,
                weight=ft.FontWeight.BOLD,
            ),
            ft.Container(height=16),  # Spacing
            ft.Row(
                stat_cards,
                alignment=ft.MainAxisAlignment.SPACE_EVENLY,
                spacing=16,
                wrap=True
            ),
        ]),
        margin=ft.margin.only(bottom=32),
    )

def _build_recent_activity(i18n_service: I18nService) -> ft.Container:
    """Build recent activity section."""
    activities = [
        {
            "icon": ft.icons.PLAY_CIRCLE_OUTLINE,
            "title_key": "home.recent_course",
            "subtitle_key": "home.mechanics_chapter",
            "time_key": "home.yesterday"
        },
        {
            "icon": ft.icons.QUIZ,
            "title_key": "home.completed_quiz",
            "subtitle_key": "home.thermodynamics_quiz",
            "time_key": "home.two_days_ago"
        },
        {
            "icon": ft.icons.ASSIGNMENT_TURNED_IN,
            "title_key": "home.solved_exercise",
            "subtitle_key": "home.kinematics_problem",
            "time_key": "home.three_days_ago"
        }
    ]

    activity_items = []
    for activity in activities:
        item = ft.ListTile(
            leading=ft.Icon(activity["icon"], color=ft.colors.PRIMARY),
            title=ft.Text(i18n_service.t(activity["title_key"])),
            subtitle=ft.Text(i18n_service.t(activity["subtitle_key"])),
            trailing=ft.Text(
                i18n_service.t(activity["time_key"]),
                size=12,
                color=ft.colors.ON_SURFACE_VARIANT
            )
        )
        activity_items.append(item)

    return ft.Container(
        content=ft.Column([
            ft.Text(
                i18n_service.t("home.recent_activity"),
                size=20,
                weight=ft.FontWeight.BOLD,
            ),
            ft.Container(height=16),  # Spacing
            ft.Card(
                content=ft.Container(
                    content=ft.Column(activity_items, spacing=0),
                    padding=8
                ),
                elevation=1
            )
        ]),
        margin=ft.margin.only(bottom=32),
    )
