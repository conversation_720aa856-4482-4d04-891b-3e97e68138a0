"""
Home view for the Hiel Physics application.

This module provides the main landing page view that serves as the entry point
for the physics education app. It includes basic layout structure and placeholder
content that will be expanded in future phases.
"""

import flet as ft
from typing import Optional, Dict, Any
from dataclasses import dataclass
from services.app_state import AppState
from services.i18n import get_i18n_service, t
from config.settings import Language


@dataclass
class RouteInfo:
    """Information about a route including path, parameters, and metadata."""
    path: str
    params: Dict[str, Any]
    query_params: Dict[str, str]


class HomeView:
    """
    Main home view class for the Hiel Physics application.
    
    Provides the landing page interface with basic layout structure,
    navigation elements, and placeholder content areas.
    """
    
    def __init__(self, router=None):
        """
        Initialize the home view.

        Args:
            router: Router instance for navigation (optional)
        """
        self.router = router
        self.app_state = AppState()
        self.i18n = get_i18n_service()

        # Listen for language changes
        self.i18n.add_language_change_listener(self._on_language_changed)

        self._build_components()
    
    def _build_components(self):
        """Build the main UI components for the home view."""
        # Header section with app branding
        self.header = ft.Container(
            content=ft.Row([
                ft.Icon(
                    ft.Icons.SCHOOL,
                    size=40,
                    color=ft.Colors.PRIMARY,
                ),
                ft.Column([
                    ft.Text(
                        t("app.name"),
                        size=28,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.PRIMARY,
                    ),
                    ft.Text(
                        t("app.welcome"),
                        size=14,
                        color=ft.Colors.SECONDARY,
                        italic=True,
                    ),
                ], spacing=0),
            ], alignment=ft.MainAxisAlignment.CENTER),
            padding=ft.padding.symmetric(vertical=20, horizontal=16),
            bgcolor=ft.Colors.SURFACE,
            border_radius=12,
            margin=ft.margin.only(bottom=20),
        )
        
        # Welcome section
        self.welcome_section = ft.Container(
            content=ft.Column([
                ft.Text(
                    t("home.welcome_message"),
                    size=20,
                    weight=ft.FontWeight.W_500,
                    text_align=ft.TextAlign.CENTER,
                ),
                ft.Divider(height=20),
                ft.Text(
                    t("home.description"),
                    size=16,
                    text_align=ft.TextAlign.CENTER,
                ),
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
            padding=20,
            bgcolor=ft.Colors.SURFACE,
            border_radius=12,
            margin=ft.margin.only(bottom=20),
        )
        
        # Main navigation cards
        self.navigation_cards = self._build_navigation_cards()
        
        # Quick stats section
        self.stats_section = self._build_stats_section()
        
        # Footer section
        self.footer = ft.Container(
            content=ft.Column([
                ft.Divider(),
                ft.Text(
                    "Hiel Physics © 2024 - تطبيق تعليمي للفيزياء",
                    size=12,
                    color=ft.Colors.SECONDARY,
                    text_align=ft.TextAlign.CENTER,
                ),
                ft.Text(
                    "Développé pour les étudiants marocains",
                    size=10,
                    color=ft.Colors.SECONDARY,
                    text_align=ft.TextAlign.CENTER,
                ),
            ], spacing=5),
            padding=20,
        )
    
    def _build_navigation_cards(self) -> ft.Container:
        """Build the main navigation cards section."""
        # Define navigation items with Arabic and French labels
        nav_items = [
            {
                "icon": ft.Icons.BOOK,
                "title_ar": "الدروس",
                "title_fr": "Cours",
                "description_ar": "دروس الفيزياء حسب المسالك",
                "description_fr": "Cours de physique par filières",
                "route": "/courses",
                "color": ft.Colors.BLUE,
            },
            {
                "icon": ft.Icons.QUIZ,
                "title_ar": "الاختبارات",
                "title_fr": "Quiz",
                "description_ar": "اختبارات تفاعلية",
                "description_fr": "Quiz interactifs",
                "route": "/quizzes",
                "color": ft.Colors.GREEN,
            },
            {
                "icon": ft.Icons.FITNESS_CENTER,
                "title_ar": "التمارين",
                "title_fr": "Exercices",
                "description_ar": "تمارين محلولة",
                "description_fr": "Exercices résolus",
                "route": "/exercises",
                "color": ft.Colors.ORANGE,
            },
            {
                "icon": ft.Icons.ASSIGNMENT,
                "title_ar": "الامتحانات",
                "title_fr": "Examens",
                "description_ar": "امتحانات وطنية وجهوية",
                "description_fr": "Examens nationaux et régionaux",
                "route": "/exams",
                "color": ft.Colors.RED,
            },
            {
                "icon": ft.Icons.SCIENCE,
                "title_ar": "المحاكاة",
                "title_fr": "Simulations",
                "description_ar": "محاكاة تفاعلية للظواهر",
                "description_fr": "Simulations interactives",
                "route": "/simulations",
                "color": ft.Colors.PURPLE,
            },
            {
                "icon": ft.Icons.SETTINGS,
                "title_ar": "الإعدادات",
                "title_fr": "Paramètres",
                "description_ar": "إعدادات التطبيق",
                "description_fr": "Paramètres de l'app",
                "route": "/settings",
                "color": ft.Colors.GREY,
            },
        ]
        
        # Create navigation cards
        cards = []
        for i in range(0, len(nav_items), 2):
            row_cards = []
            for j in range(2):
                if i + j < len(nav_items):
                    item = nav_items[i + j]
                    card = self._create_nav_card(item)
                    row_cards.append(card)
            
            cards.append(
                ft.Row(
                    row_cards,
                    alignment=ft.MainAxisAlignment.SPACE_EVENLY,
                    spacing=10,
                )
            )
        
        return ft.Container(
            content=ft.Column(cards, spacing=15),
            padding=20,
        )
    
    def _create_nav_card(self, item: dict) -> ft.Container:
        """Create a navigation card for a menu item."""
        def on_card_click(e):
            if self.router:
                self.router.navigate_to(item["route"])
            else:
                print(f"Navigate to: {item['route']}")
        
        return ft.Container(
            content=ft.Column([
                ft.Icon(
                    item["icon"],
                    size=40,
                    color=item["color"],
                ),
                ft.Text(
                    item["title_ar"],
                    size=16,
                    weight=ft.FontWeight.BOLD,
                    text_align=ft.TextAlign.CENTER,
                ),
                ft.Text(
                    item["title_fr"],
                    size=14,
                    color=ft.Colors.SECONDARY,
                    text_align=ft.TextAlign.CENTER,
                ),
                ft.Text(
                    item["description_ar"],
                    size=12,
                    text_align=ft.TextAlign.CENTER,
                    max_lines=2,
                ),
                ft.Text(
                    item["description_fr"],
                    size=10,
                    color=ft.Colors.SECONDARY,
                    text_align=ft.TextAlign.CENTER,
                    max_lines=2,
                ),
            ], 
            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            spacing=5,
            ),
            width=160,
            height=180,
            padding=15,
            bgcolor=ft.Colors.SURFACE,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.GREY_300),
            on_click=on_card_click,
            ink=True,
        )
    
    def _build_stats_section(self) -> ft.Container:
        """Build the quick stats section."""
        stats = [
            {"label_ar": "المسالك", "label_fr": "Filières", "value": "4", "icon": ft.Icons.SCHOOL},
            {"label_ar": "الدروس", "label_fr": "Cours", "value": "120+", "icon": ft.Icons.BOOK},
            {"label_ar": "التمارين", "label_fr": "Exercices", "value": "500+", "icon": ft.Icons.FITNESS_CENTER},
            {"label_ar": "المحاكاة", "label_fr": "Simulations", "value": "25+", "icon": ft.Icons.SCIENCE},
        ]
        
        stat_cards = []
        for stat in stats:
            card = ft.Container(
                content=ft.Column([
                    ft.Icon(stat["icon"], size=24, color=ft.Colors.PRIMARY),
                    ft.Text(
                        stat["value"],
                        size=20,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.PRIMARY,
                    ),
                    ft.Text(
                        stat["label_ar"],
                        size=12,
                        text_align=ft.TextAlign.CENTER,
                    ),
                    ft.Text(
                        stat["label_fr"],
                        size=10,
                        color=ft.Colors.SECONDARY,
                        text_align=ft.TextAlign.CENTER,
                    ),
                ], 
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                spacing=2,
                ),
                width=80,
                height=100,
                padding=10,
                bgcolor=ft.Colors.SURFACE,
                border_radius=8,
            )
            stat_cards.append(card)
        
        return ft.Container(
            content=ft.Column([
                ft.Text(
                    "إحصائيات سريعة - Statistiques rapides",
                    size=16,
                    weight=ft.FontWeight.W_500,
                    text_align=ft.TextAlign.CENTER,
                ),
                ft.Row(
                    stat_cards,
                    alignment=ft.MainAxisAlignment.SPACE_EVENLY,
                    spacing=10,
                ),
            ], spacing=15),
            padding=20,
            bgcolor=ft.Colors.SURFACE,
            border_radius=12,
            margin=ft.margin.only(bottom=20),
        )
    
    def build(self, route_info: Optional[RouteInfo] = None) -> ft.View:
        """
        Build and return the complete home view.
        
        Args:
            route_info: Optional route information
            
        Returns:
            Flet View object for the home page
        """
        # Main content column
        main_content = ft.Column([
            self.header,
            self.welcome_section,
            self.navigation_cards,
            self.stats_section,
            self.footer,
        ], 
        spacing=0,
        scroll=ft.ScrollMode.AUTO,
        )
        
        # Create the view with app bar
        return ft.View(
            route="/home" if route_info is None else route_info.path,
            controls=[
                ft.AppBar(
                    title=ft.Text("Hiel Physics - الصفحة الرئيسية"),
                    bgcolor=ft.Colors.PRIMARY,
                    color=ft.Colors.WHITE,
                    center_title=True,
                    actions=[
                        ft.IconButton(
                            ft.Icons.LANGUAGE,
                            tooltip="تغيير اللغة / Changer la langue",
                            on_click=self._on_language_toggle,
                        ),
                        ft.IconButton(
                            ft.Icons.PERSON,
                            tooltip="الملف الشخصي / Profil",
                            on_click=self._on_profile_click,
                        ),
                    ],
                ),
                ft.Container(
                    content=main_content,
                    padding=ft.padding.symmetric(horizontal=16, vertical=8),
                    expand=True,
                ),
            ],
            bgcolor=ft.Colors.BACKGROUND,
        )
    
    def _on_language_toggle(self, e):
        """Handle language toggle button click."""
        current_language = self.i18n.get_current_language()
        new_language = Language.FRENCH if current_language == Language.ARABIC else Language.ARABIC

        # Update language
        self.i18n.set_language(new_language)
        self.app_state.set_language(new_language)

        # Update RTL setting
        if hasattr(self, 'page') and self.page:
            self.page.rtl = new_language == Language.ARABIC
            self.page.update()

    def _on_language_changed(self, language: Language):
        """Handle language change events."""
        # Rebuild components with new language
        self._build_components()

        # Update page if available
        if hasattr(self, 'page') and self.page:
            self.page.update()
    
    def _on_profile_click(self, e):
        """Handle profile button click."""
        if self.router:
            self.router.navigate_to("/profile")
        else:
            print("Navigate to profile")


def create_home_view(route_info: RouteInfo, router=None) -> ft.View:
    """
    Factory function to create a home view.
    
    Args:
        route_info: Route information from the router
        router: Router instance for navigation
        
    Returns:
        Flet View object for the home page
    """
    home_view = HomeView(router=router)
    return home_view.build(route_info)


# Convenience function for direct usage
def build_home_view(router=None) -> ft.View:
    """
    Build a home view without route info (for direct usage).
    
    Args:
        router: Router instance for navigation
        
    Returns:
        Flet View object for the home page
    """
    home_view = HomeView(router=router)
    return home_view.build()