"""
Quizzes view - placeholder implementation for demonstration.
"""

import flet as ft
from services.i18n import I18nService


def create_quizzes_view(i18n_service: I18nService) -> ft.Control:
    """Create the quizzes view.
    
    Args:
        i18n_service: Internationalization service
        
    Returns:
        Quizzes view control
    """
    return ft.Container(
        content=ft.Column([
            # Page title
            ft.Text(
                i18n_service.t("navigation.quizzes"),
                size=32,
                weight=ft.FontWeight.BOLD
            ),
            
            # Page description
            ft.Text(
                i18n_service.t("quizzes.description"),
                size=16,
                color=ft.colors.ON_SURFACE_VARIANT
            ),
            
            ft.Container(height=24),  # Spacing
            
            # Quick stats
            ft.Row([
                ft.Card(
                    content=ft.Container(
                        content=ft.Column([
                            ft.Text("24", size=32, weight=ft.FontWeight.BOLD, color=ft.colors.PRIMARY),
                            ft.Text(i18n_service.t("quizzes.completed"), size=14)
                        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                        padding=16,
                        width=120
                    )
                ),
                ft.Card(
                    content=ft.Container(
                        content=ft.Column([
                            ft.Text("85%", size=32, weight=ft.FontWeight.BOLD, color=ft.colors.SECONDARY),
                            ft.Text(i18n_service.t("quizzes.average_score"), size=14)
                        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                        padding=16,
                        width=120
                    )
                ),
                ft.Card(
                    content=ft.Container(
                        content=ft.Column([
                            ft.Text("12", size=32, weight=ft.FontWeight.BOLD, color=ft.colors.TERTIARY),
                            ft.Text(i18n_service.t("quizzes.available"), size=14)
                        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                        padding=16,
                        width=120
                    )
                )
            ], spacing=16),
            
            ft.Container(height=32),  # Spacing
            
            # Available quizzes
            ft.Text(
                i18n_service.t("quizzes.available_quizzes"),
                size=24,
                weight=ft.FontWeight.BOLD
            ),
            
            ft.Container(height=16),  # Spacing
            
            # Quiz cards
            ft.Row([
                ft.Card(
                    content=ft.Container(
                        content=ft.Column([
                            ft.Icon(ft.icons.QUIZ, size=48, color=ft.colors.PRIMARY),
                            ft.Text(
                                i18n_service.t("quizzes.mechanics_quiz"),
                                size=18,
                                weight=ft.FontWeight.BOLD
                            ),
                            ft.Text(
                                i18n_service.t("quizzes.questions_15"),
                                size=14,
                                color=ft.colors.ON_SURFACE_VARIANT
                            ),
                            ft.Text(
                                i18n_service.t("quizzes.duration_20min"),
                                size=14,
                                color=ft.colors.ON_SURFACE_VARIANT
                            ),
                            ft.ElevatedButton(
                                text=i18n_service.t("quizzes.start_quiz"),
                                icon=ft.icons.PLAY_ARROW
                            )
                        ], 
                        horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                        spacing=8
                        ),
                        padding=20,
                        width=250
                    ),
                    elevation=2
                ),
                
                ft.Card(
                    content=ft.Container(
                        content=ft.Column([
                            ft.Icon(ft.icons.QUIZ, size=48, color=ft.colors.SECONDARY),
                            ft.Text(
                                i18n_service.t("quizzes.thermodynamics_quiz"),
                                size=18,
                                weight=ft.FontWeight.BOLD
                            ),
                            ft.Text(
                                i18n_service.t("quizzes.questions_20"),
                                size=14,
                                color=ft.colors.ON_SURFACE_VARIANT
                            ),
                            ft.Text(
                                i18n_service.t("quizzes.duration_25min"),
                                size=14,
                                color=ft.colors.ON_SURFACE_VARIANT
                            ),
                            ft.ElevatedButton(
                                text=i18n_service.t("quizzes.start_quiz"),
                                icon=ft.icons.PLAY_ARROW
                            )
                        ], 
                        horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                        spacing=8
                        ),
                        padding=20,
                        width=250
                    ),
                    elevation=2
                )
            ], 
            wrap=True,
            spacing=16,
            run_spacing=16
            ),
            
            ft.Container(height=32),  # Spacing
            
            # Recent results
            ft.Text(
                i18n_service.t("quizzes.recent_results"),
                size=24,
                weight=ft.FontWeight.BOLD
            ),
            
            ft.Container(height=16),  # Spacing
            
            # Results list
            ft.Column([
                ft.ListTile(
                    leading=ft.Icon(ft.icons.CHECK_CIRCLE, color=ft.colors.GREEN),
                    title=ft.Text(i18n_service.t("quizzes.optics_quiz")),
                    subtitle=ft.Text(i18n_service.t("quizzes.score_92")),
                    trailing=ft.Text("2 " + i18n_service.t("common.days_ago"))
                ),
                ft.ListTile(
                    leading=ft.Icon(ft.icons.CHECK_CIRCLE, color=ft.colors.GREEN),
                    title=ft.Text(i18n_service.t("quizzes.waves_quiz")),
                    subtitle=ft.Text(i18n_service.t("quizzes.score_78")),
                    trailing=ft.Text("5 " + i18n_service.t("common.days_ago"))
                ),
                ft.ListTile(
                    leading=ft.Icon(ft.icons.CHECK_CIRCLE, color=ft.colors.ORANGE),
                    title=ft.Text(i18n_service.t("quizzes.electricity_quiz")),
                    subtitle=ft.Text(i18n_service.t("quizzes.score_65")),
                    trailing=ft.Text("1 " + i18n_service.t("common.week_ago"))
                )
            ])
        ], 
        spacing=8,
        scroll=ft.ScrollMode.AUTO
        ),
        padding=ft.padding.all(24),
        expand=True
    )
