"""
Custom navigation rail component with collapsible state and animations.
"""

import flet as ft
from typing import Optional, Callable

from config.settings import (
    NAVIGATION_ITEMS, LAYOUT_DIMENSIONS, ANIMATION_DURATION_MS
)
from services.i18n import I18nService
from services.theme_service import ThemeService
from services.app_state import AppState


class NavigationRail(ft.UserControl):
    """Custom navigation rail with enhanced features."""
    
    def __init__(
        self,
        page: ft.Page,
        i18n_service: I18nService,
        theme_service: ThemeService,
        selected_index: int = 0,
        on_destination_click: Optional[Callable[[int], None]] = None
    ):
        """Initialize the navigation rail.
        
        Args:
            page: Flet page instance
            i18n_service: Internationalization service
            theme_service: Theme service
            selected_index: Initially selected index
            on_destination_click: Callback for destination clicks
        """
        super().__init__()
        self.page = page
        self.i18n_service = i18n_service
        self.theme_service = theme_service
        self.selected_index = selected_index
        self.on_destination_click = on_destination_click
        
        # Get app state for collapsed preference
        self.app_state = AppState()
        self.is_collapsed = self.app_state.get_navigation_collapsed()
        
        # Navigation rail component
        self.rail: Optional[ft.NavigationRail] = None
        
        # Listen for theme changes
        self.theme_service.add_theme_change_listener(self._on_theme_change)
        
        # Listen for language changes
        self.i18n_service.add_language_change_listener(self._on_language_change)
    
    def build(self) -> ft.Control:
        """Build the navigation rail."""
        # Create destinations
        destinations = []
        for item in NAVIGATION_ITEMS:
            # Get localized label
            label = self.i18n_service.t(item.label_key)
            
            destination = ft.NavigationRailDestination(
                icon=item.icon,
                selected_icon=item.selected_icon or item.icon,
                label=label if not self.is_collapsed else None,
                label_content=ft.Text(label) if not self.is_collapsed else None
            )
            destinations.append(destination)
        
        # Create navigation rail
        self.rail = ft.NavigationRail(
            selected_index=self.selected_index,
            label_type=ft.NavigationRailLabelType.ALL if not self.is_collapsed else ft.NavigationRailLabelType.NONE,
            min_width=LAYOUT_DIMENSIONS["navigation_rail_collapsed_width"] if self.is_collapsed else LAYOUT_DIMENSIONS["navigation_rail_width"],
            min_extended_width=LAYOUT_DIMENSIONS["navigation_rail_width"],
            group_alignment=-1.0,
            destinations=destinations,
            on_change=self._handle_destination_change,
            leading=self._create_toggle_button(),
            bgcolor=self.theme_service.get_theme_colors()["surface"],
            indicator_color=self.theme_service.get_theme_colors()["secondary_container"],
            indicator_shape=ft.RoundedRectangleBorder(radius=12)
        )
        
        # Wrap in animated container for smooth width transitions
        return ft.AnimatedContainer(
            content=self.rail,
            width=LAYOUT_DIMENSIONS["navigation_rail_collapsed_width"] if self.is_collapsed else LAYOUT_DIMENSIONS["navigation_rail_width"],
            duration=ANIMATION_DURATION_MS,
            curve=ft.AnimationCurve.EASE_IN_OUT
        )
    
    def _create_toggle_button(self) -> ft.Control:
        """Create the collapse/expand toggle button."""
        icon = ft.icons.MENU_OPEN if self.is_collapsed else ft.icons.MENU
        tooltip = self.i18n_service.t("layout.expand_navigation" if self.is_collapsed else "layout.collapse_navigation")
        
        return ft.IconButton(
            icon=icon,
            tooltip=tooltip,
            on_click=self._handle_toggle_click,
            icon_color=self.theme_service.get_theme_colors()["on_surface"]
        )
    
    def set_selected_index(self, index: int) -> None:
        """Set the selected index.
        
        Args:
            index: Index to select
        """
        if self.selected_index != index:
            self.selected_index = index
            if self.rail:
                self.rail.selected_index = index
                self.update()
    
    def toggle_collapsed(self) -> None:
        """Toggle the collapsed state."""
        self.is_collapsed = not self.is_collapsed
        self.app_state.set_navigation_collapsed(self.is_collapsed)
        self.update()
    
    def _handle_destination_change(self, e) -> None:
        """Handle destination selection change.
        
        Args:
            e: Change event
        """
        if self.on_destination_click:
            self.on_destination_click(e.control.selected_index)
    
    def _handle_toggle_click(self, e) -> None:
        """Handle toggle button click.
        
        Args:
            e: Click event
        """
        self.toggle_collapsed()
    
    def _on_theme_change(self) -> None:
        """Handle theme change."""
        self.update()
    
    def _on_language_change(self) -> None:
        """Handle language change."""
        self.update()
    
    def will_unmount(self) -> None:
        """Called when control is unmounted."""
        # Remove listeners
        self.theme_service.remove_theme_change_listener(self._on_theme_change)
        self.i18n_service.remove_language_change_listener(self._on_language_change)
