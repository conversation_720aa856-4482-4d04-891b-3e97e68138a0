"""
Custom navigation drawer component for mobile/tablet responsive design.
"""

import flet as ft
from typing import Optional, Callable

from config.settings import (
    NAVIGATION_ITEMS, LAYOUT_DIMENSIONS, ANIMATION_DURATION_MS
)
from services.i18n import I18nService
from services.theme_service import ThemeService


class NavigationDrawer(ft.UserControl):
    """Custom navigation drawer for mobile/tablet layouts."""
    
    def __init__(
        self,
        page: ft.Page,
        i18n_service: I18nService,
        theme_service: ThemeService,
        selected_index: int = 0,
        on_item_click: Optional[Callable[[int], None]] = None,
        on_dismiss: Optional[Callable[[], None]] = None
    ):
        """Initialize the navigation drawer.
        
        Args:
            page: Flet page instance
            i18n_service: Internationalization service
            theme_service: Theme service
            selected_index: Initially selected index
            on_item_click: Callback for item clicks
            on_dismiss: Callback for drawer dismiss
        """
        super().__init__()
        self.page = page
        self.i18n_service = i18n_service
        self.theme_service = theme_service
        self.selected_index = selected_index
        self.on_item_click = on_item_click
        self.on_dismiss = on_dismiss
        
        # Drawer state
        self.is_visible = False
        
        # Drawer components
        self.backdrop: Optional[ft.Container] = None
        self.drawer_container: Optional[ft.Container] = None
        
        # Listen for theme changes
        self.theme_service.add_theme_change_listener(self._on_theme_change)
        
        # Listen for language changes
        self.i18n_service.add_language_change_listener(self._on_language_change)
    
    def build(self) -> ft.Control:
        """Build the navigation drawer."""
        # Create backdrop
        self.backdrop = ft.Container(
            bgcolor=ft.colors.with_opacity(0.5, ft.colors.BLACK),
            visible=self.is_visible,
            on_click=self._handle_backdrop_click,
            animate_opacity=ft.Animation(ANIMATION_DURATION_MS, ft.AnimationCurve.EASE_IN_OUT)
        )
        
        # Create drawer content
        drawer_content = self._create_drawer_content()
        
        # Create drawer container
        self.drawer_container = ft.Container(
            content=drawer_content,
            width=LAYOUT_DIMENSIONS["navigation_drawer_width"],
            height=self.page.height,
            bgcolor=self.theme_service.get_theme_colors()["surface"],
            border_radius=ft.border_radius.only(top_right=16, bottom_right=16),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=16,
                color=ft.colors.with_opacity(0.15, ft.colors.BLACK),
                offset=ft.Offset(4, 0)
            ),
            offset=ft.transform.Offset(-1 if not self.is_visible else 0, 0),
            animate_offset=ft.Animation(ANIMATION_DURATION_MS, ft.AnimationCurve.EASE_IN_OUT)
        )
        
        # Return stack with backdrop and drawer
        return ft.Stack([
            self.backdrop,
            self.drawer_container
        ], visible=False)
    
    def _create_drawer_content(self) -> ft.Control:
        """Create the drawer content."""
        # Create header
        header = ft.Container(
            content=ft.Column([
                ft.Text(
                    self.i18n_service.t("app.name"),
                    size=24,
                    weight=ft.FontWeight.BOLD,
                    color=self.theme_service.get_theme_colors()["on_surface"]
                ),
                ft.Text(
                    self.i18n_service.t("app.tagline"),
                    size=14,
                    color=self.theme_service.get_theme_colors()["on_surface_variant"]
                )
            ], spacing=4),
            padding=ft.padding.all(24),
            bgcolor=self.theme_service.get_theme_colors()["surface_variant"]
        )
        
        # Create navigation items
        nav_items = []
        for i, item in enumerate(NAVIGATION_ITEMS):
            is_selected = i == self.selected_index
            
            nav_item = ft.Container(
                content=ft.Row([
                    ft.Icon(
                        item.selected_icon if is_selected else item.icon,
                        color=self.theme_service.get_theme_colors()["on_secondary_container" if is_selected else "on_surface"]
                    ),
                    ft.Text(
                        self.i18n_service.t(item.label_key),
                        color=self.theme_service.get_theme_colors()["on_secondary_container" if is_selected else "on_surface"],
                        weight=ft.FontWeight.BOLD if is_selected else ft.FontWeight.NORMAL
                    )
                ], spacing=12),
                padding=ft.padding.symmetric(horizontal=16, vertical=12),
                margin=ft.margin.symmetric(horizontal=12, vertical=2),
                bgcolor=self.theme_service.get_theme_colors()["secondary_container"] if is_selected else None,
                border_radius=12,
                on_click=lambda e, index=i: self._handle_item_click(index),
                ink=True
            )
            nav_items.append(nav_item)
        
        # Create navigation list
        navigation_list = ft.Column(
            nav_items,
            spacing=0,
            scroll=ft.ScrollMode.AUTO
        )
        
        # Return complete drawer content
        return ft.Column([
            header,
            ft.Divider(height=1),
            ft.Container(
                content=navigation_list,
                expand=True,
                padding=ft.padding.symmetric(vertical=8)
            )
        ], spacing=0)
    
    def show(self) -> None:
        """Show the drawer."""
        if not self.is_visible:
            self.is_visible = True
            self.visible = True
            self.backdrop.visible = True
            self.backdrop.opacity = 1.0
            self.drawer_container.offset = ft.transform.Offset(0, 0)
            self.update()
    
    def hide(self) -> None:
        """Hide the drawer."""
        if self.is_visible:
            self.is_visible = False
            self.backdrop.opacity = 0.0
            self.drawer_container.offset = ft.transform.Offset(-1, 0)
            self.update()
            
            # Hide completely after animation
            def hide_complete():
                self.visible = False
                self.backdrop.visible = False
                self.update()
            
            # Schedule hide after animation duration
            self.page.run_thread(lambda: (
                self.page.sleep(ANIMATION_DURATION_MS / 1000),
                hide_complete()
            ))
    
    def toggle(self) -> None:
        """Toggle the drawer visibility."""
        if self.is_visible:
            self.hide()
        else:
            self.show()
    
    def set_selected_index(self, index: int) -> None:
        """Set the selected index.
        
        Args:
            index: Index to select
        """
        if self.selected_index != index:
            self.selected_index = index
            self.update()
    
    def _handle_item_click(self, index: int) -> None:
        """Handle navigation item click.
        
        Args:
            index: Index of clicked item
        """
        if self.on_item_click:
            self.on_item_click(index)
    
    def _handle_backdrop_click(self, e) -> None:
        """Handle backdrop click (dismiss drawer).
        
        Args:
            e: Click event
        """
        self.hide()
        if self.on_dismiss:
            self.on_dismiss()
    
    def _on_theme_change(self) -> None:
        """Handle theme change."""
        self.update()
    
    def _on_language_change(self) -> None:
        """Handle language change."""
        self.update()
    
    def will_unmount(self) -> None:
        """Called when control is unmounted."""
        # Remove listeners
        self.theme_service.remove_theme_change_listener(self._on_theme_change)
        self.i18n_service.remove_language_change_listener(self._on_language_change)
