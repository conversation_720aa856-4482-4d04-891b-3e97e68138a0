import flet as ft
from typing import Callable, Optional

from services.app_state import AppState
from services.i18n import get_i18n_service, t
from config.settings import EducationalTrack


class TrackSelector(ft.UserControl):
    """Reusable educational track selector component."""
    
    def __init__(
        self,
        on_track_change: Optional[Callable[[EducationalTrack], None]] = None,
        mode: str = "list",  # "list", "cards", or "dropdown"
        show_descriptions: bool = True
    ):
        super().__init__()
        self.app_state = AppState()
        self.i18n = get_i18n_service()
        self.on_track_change = on_track_change
        self.mode = mode
        self.show_descriptions = show_descriptions
        self.current_track = self.app_state.get_educational_track()
    
    def build(self):
        """Build the track selector component."""
        if self.mode == "dropdown":
            return self._build_dropdown()
        elif self.mode == "cards":
            return self._build_cards()
        else:
            return self._build_list()
    
    def _build_list(self) -> ft.Column:
        """Build list-based track selector."""
        list_items = []
        
        for track in EducationalTrack:
            track_key = track.value.lower()
            track_name = t(f"onboarding.track.tracks.{track_key}.name")
            track_description = t(f"onboarding.track.tracks.{track_key}.description")
            
            is_selected = self.current_track == track
            
            # Create list tile
            content = ft.Column([
                ft.Text(
                    track_name,
                    size=16,
                    weight=ft.FontWeight.BOLD
                )
            ])
            
            if self.show_descriptions:
                content.controls.append(
                    ft.Text(
                        track_description,
                        size=14,
                        color=ft.Colors.GREY_600
                    )
                )
            
            list_tile = ft.Container(
                content=ft.Row([
                    ft.Radio(
                        value=track.value,
                        group_value=self.current_track.value if self.current_track else None,
                        on_change=self._on_radio_change
                    ),
                    ft.Expanded(child=content)
                ]),
                padding=ft.padding.all(12),
                border_radius=8,
                bgcolor=ft.Colors.BLUE_100 if is_selected else None,
                ink=True,
                on_click=lambda e, t=track: self._handle_track_change(t)
            )
            
            list_items.append(list_tile)
        
        return ft.Column([
            ft.Text(
                t("settings.educational_track"),
                size=16,
                weight=ft.FontWeight.BOLD
            ),
            ft.Column(list_items, spacing=8)
        ], spacing=12)
    
    def _build_cards(self) -> ft.Column:
        """Build card-based track selector."""
        cards = []
        
        for track in EducationalTrack:
            track_key = track.value.lower()
            track_name = t(f"onboarding.track.tracks.{track_key}.name")
            track_description = t(f"onboarding.track.tracks.{track_key}.description")
            
            is_selected = self.current_track == track
            
            content = ft.Column([
                ft.Text(
                    track_name,
                    size=16,
                    weight=ft.FontWeight.BOLD,
                    text_align=ft.TextAlign.CENTER
                )
            ],
            alignment=ft.MainAxisAlignment.CENTER,
            horizontal_alignment=ft.CrossAxisAlignment.CENTER
            )
            
            if self.show_descriptions:
                content.controls.append(
                    ft.Text(
                        track_description,
                        size=14,
                        color=ft.Colors.GREY_600,
                        text_align=ft.TextAlign.CENTER
                    )
                )
            
            card = ft.Card(
                content=ft.Container(
                    content=content,
                    padding=ft.padding.all(16),
                    alignment=ft.alignment.center,
                    bgcolor=ft.Colors.BLUE_100 if is_selected else None,
                    border_radius=12,
                    ink=True,
                    on_click=lambda e, t=track: self._handle_track_change(t)
                ),
                elevation=2 if not is_selected else 8,
                margin=ft.margin.symmetric(vertical=4)
            )
            
            cards.append(card)
        
        return ft.Column([
            ft.Text(
                t("settings.educational_track"),
                size=16,
                weight=ft.FontWeight.BOLD
            ),
            ft.Column(cards, spacing=8)
        ], spacing=12)
    
    def _build_dropdown(self) -> ft.Column:
        """Build dropdown track selector."""
        options = []
        
        for track in EducationalTrack:
            track_key = track.value.lower()
            track_name = t(f"onboarding.track.tracks.{track_key}.name")
            
            options.append(
                ft.dropdown.Option(
                    key=track.value,
                    text=track_name
                )
            )
        
        dropdown = ft.Dropdown(
            options=options,
            value=self.current_track.value if self.current_track else None,
            on_change=self._on_dropdown_change,
            width=300
        )
        
        return ft.Column([
            ft.Text(
                t("settings.educational_track"),
                size=16,
                weight=ft.FontWeight.BOLD
            ),
            dropdown
        ], spacing=12)
    
    def _on_radio_change(self, e) -> None:
        """Handle radio button change."""
        if e.control.value:
            track = EducationalTrack(e.control.value)
            self._handle_track_change(track)
    
    def _on_dropdown_change(self, e) -> None:
        """Handle dropdown change."""
        if e.control.value:
            track = EducationalTrack(e.control.value)
            self._handle_track_change(track)
    
    def _handle_track_change(self, track: EducationalTrack) -> None:
        """Handle track change event."""
        if track != self.current_track:
            self.current_track = track
            
            # Update app state
            self.app_state.set_educational_track(track)
            
            # Update UI
            self.update()
            
            # Call callback if provided
            if self.on_track_change:
                self.on_track_change(track)
    
    def set_track(self, track: EducationalTrack) -> None:
        """Programmatically set the track."""
        self.current_track = track
        self.update()
    
    def get_current_track(self) -> Optional[EducationalTrack]:
        """Get the currently selected track."""
        return self.current_track


class TrackSelectorCards(ft.UserControl):
    """Simplified card-based track selector for onboarding."""
    
    def __init__(
        self,
        on_track_change: Optional[Callable[[EducationalTrack], None]] = None,
        selected_track: Optional[EducationalTrack] = None
    ):
        super().__init__()
        self.on_track_change = on_track_change
        self.selected_track = selected_track
        self.i18n = get_i18n_service()
    
    def build(self):
        """Build the simplified card-based track selector."""
        cards = []
        
        for track in EducationalTrack:
            track_key = track.value.lower()
            track_name = t(f"onboarding.track.tracks.{track_key}.name")
            track_description = t(f"onboarding.track.tracks.{track_key}.description")
            
            is_selected = self.selected_track == track
            
            card = ft.Card(
                content=ft.Container(
                    content=ft.Column([
                        ft.Text(
                            track_name,
                            size=16,
                            weight=ft.FontWeight.BOLD,
                            text_align=ft.TextAlign.CENTER
                        ),
                        ft.Text(
                            track_description,
                            size=14,
                            color=ft.Colors.GREY_600,
                            text_align=ft.TextAlign.CENTER
                        )
                    ],
                    alignment=ft.MainAxisAlignment.CENTER,
                    horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                    spacing=8
                    ),
                    padding=ft.padding.all(16),
                    alignment=ft.alignment.center,
                    bgcolor=ft.Colors.BLUE_100 if is_selected else None,
                    border_radius=12,
                    ink=True,
                    on_click=lambda e, t=track: self._on_card_click(t)
                ),
                elevation=2 if not is_selected else 8,
                margin=ft.margin.symmetric(vertical=4)
            )
            
            cards.append(card)
        
        return ft.Column(cards, spacing=8)
    
    def _on_card_click(self, track: EducationalTrack) -> None:
        """Handle card click."""
        self.selected_track = track
        self.update()
        
        if self.on_track_change:
            self.on_track_change(track)
    
    def set_selected_track(self, track: EducationalTrack) -> None:
        """Set the selected track."""
        self.selected_track = track
        self.update()
    
    def get_selected_track(self) -> Optional[EducationalTrack]:
        """Get the selected track."""
        return self.selected_track
