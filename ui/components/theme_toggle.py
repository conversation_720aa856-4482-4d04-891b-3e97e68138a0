"""
Theme toggle component for switching between light, dark, and system themes.
"""

import flet as ft
from typing import Optional

from services.theme_service import ThemeService, ThemeMode
from services.i18n import I18nService


class ThemeToggle(ft.UserControl):
    """Theme toggle component with dropdown for theme mode selection."""
    
    def __init__(
        self,
        theme_service: ThemeService,
        i18n_service: I18nService
    ):
        """Initialize the theme toggle.
        
        Args:
            theme_service: Theme service
            i18n_service: Internationalization service
        """
        super().__init__()
        self.theme_service = theme_service
        self.i18n_service = i18n_service
        
        # Theme toggle component
        self.dropdown: Optional[ft.Dropdown] = None
        
        # Listen for theme changes
        self.theme_service.add_theme_change_listener(self._on_theme_change)
        
        # Listen for language changes
        self.i18n_service.add_language_change_listener(self._on_language_change)
    
    def build(self) -> ft.Control:
        """Build the theme toggle."""
        current_mode = self.theme_service.get_current_theme_mode()
        
        # Create dropdown options
        options = [
            ft.dropdown.Option(
                key=ThemeMode.LIGHT.value,
                text=self.i18n_service.t("theme.light"),
                content=ft.Row([
                    ft.Icon(ft.icons.LIGHT_MODE, size=16),
                    ft.Text(self.i18n_service.t("theme.light"))
                ], spacing=8)
            ),
            ft.dropdown.Option(
                key=ThemeMode.DARK.value,
                text=self.i18n_service.t("theme.dark"),
                content=ft.Row([
                    ft.Icon(ft.icons.DARK_MODE, size=16),
                    ft.Text(self.i18n_service.t("theme.dark"))
                ], spacing=8)
            ),
            ft.dropdown.Option(
                key=ThemeMode.SYSTEM.value,
                text=self.i18n_service.t("theme.system"),
                content=ft.Row([
                    ft.Icon(ft.icons.BRIGHTNESS_AUTO, size=16),
                    ft.Text(self.i18n_service.t("theme.system"))
                ], spacing=8)
            )
        ]
        
        # Create dropdown
        self.dropdown = ft.Dropdown(
            value=current_mode.value,
            options=options,
            width=140,
            height=40,
            text_size=14,
            content_padding=ft.padding.symmetric(horizontal=12, vertical=8),
            on_change=self._handle_theme_change,
            tooltip=self.i18n_service.t("theme.toggle_tooltip")
        )
        
        return self.dropdown
    
    def _handle_theme_change(self, e) -> None:
        """Handle theme mode change.
        
        Args:
            e: Change event
        """
        try:
            new_mode = ThemeMode(e.control.value)
            self.theme_service.set_theme_mode(new_mode)
        except ValueError:
            # Invalid theme mode, reset to current
            self.dropdown.value = self.theme_service.get_current_theme_mode().value
            self.update()
    
    def _on_theme_change(self) -> None:
        """Handle theme change from service."""
        if self.dropdown:
            current_mode = self.theme_service.get_current_theme_mode()
            if self.dropdown.value != current_mode.value:
                self.dropdown.value = current_mode.value
                self.update()
    
    def _on_language_change(self) -> None:
        """Handle language change."""
        self.update()
    
    def will_unmount(self) -> None:
        """Called when control is unmounted."""
        # Remove listeners
        self.theme_service.remove_theme_change_listener(self._on_theme_change)
        self.i18n_service.remove_language_change_listener(self._on_language_change)
