import flet as ft
from typing import Callable, Optional

from services.app_state import AppState
from services.i18n import get_i18n_service
from config.settings import Language


class LanguageSelector(ft.UserControl):
    """Reusable language selector component."""
    
    def __init__(
        self,
        on_language_change: Optional[Callable[[Language], None]] = None,
        mode: str = "radio",  # "radio" or "dropdown"
        show_flags: bool = False
    ):
        super().__init__()
        self.app_state = AppState()
        self.i18n = get_i18n_service()
        self.on_language_change = on_language_change
        self.mode = mode
        self.show_flags = show_flags
        self.current_language = self.app_state.get_language()
        
        # Language display information
        self.language_info = {
            Language.ARABIC: {
                "native_name": "العربية",
                "english_name": "Arabic",
                "flag": "🇲🇦"  # Morocco flag as proxy
            },
            Language.FRENCH: {
                "native_name": "Français",
                "english_name": "French",
                "flag": "🇫🇷"
            }
        }
    
    def build(self):
        """Build the language selector component."""
        if self.mode == "dropdown":
            return self._build_dropdown()
        else:
            return self._build_radio_group()
    
    def _build_radio_group(self) -> ft.Column:
        """Build radio button group for language selection."""
        radio_buttons = []
        
        for language in Language:
            info = self.language_info[language]
            
            # Create label with flag if enabled
            label_parts = []
            if self.show_flags:
                label_parts.append(info["flag"])
            label_parts.append(info["native_name"])
            
            label = " ".join(label_parts)
            
            radio_button = ft.Radio(
                value=language.value,
                label=label,
                group_value=self.current_language.value if self.current_language else None
            )
            radio_buttons.append(radio_button)
        
        radio_group = ft.RadioGroup(
            content=ft.Column(radio_buttons, spacing=8),
            on_change=self._on_radio_change
        )
        
        return ft.Column([
            ft.Text(
                self.i18n.get_text("settings.language"),
                size=16,
                weight=ft.FontWeight.BOLD
            ),
            radio_group
        ], spacing=12)
    
    def _build_dropdown(self) -> ft.Column:
        """Build dropdown for language selection."""
        options = []
        
        for language in Language:
            info = self.language_info[language]
            
            # Create option text with flag if enabled
            text_parts = []
            if self.show_flags:
                text_parts.append(info["flag"])
            text_parts.append(info["native_name"])
            
            text = " ".join(text_parts)
            
            options.append(
                ft.dropdown.Option(
                    key=language.value,
                    text=text
                )
            )
        
        dropdown = ft.Dropdown(
            options=options,
            value=self.current_language.value if self.current_language else None,
            on_change=self._on_dropdown_change,
            width=200
        )
        
        return ft.Column([
            ft.Text(
                self.i18n.get_text("settings.language"),
                size=16,
                weight=ft.FontWeight.BOLD
            ),
            dropdown
        ], spacing=12)
    
    def _on_radio_change(self, e) -> None:
        """Handle radio button change."""
        if e.control.value:
            language = Language(e.control.value)
            self._handle_language_change(language)
    
    def _on_dropdown_change(self, e) -> None:
        """Handle dropdown change."""
        if e.control.value:
            language = Language(e.control.value)
            self._handle_language_change(language)
    
    def _handle_language_change(self, language: Language) -> None:
        """Handle language change event."""
        if language != self.current_language:
            self.current_language = language
            
            # Update app state
            self.app_state.set_language(language)
            
            # Update i18n service
            self.i18n.set_language(language)
            
            # Set RTL for Arabic
            if self.page:
                self.page.rtl = language == Language.ARABIC
                self.page.update()
            
            # Call callback if provided
            if self.on_language_change:
                self.on_language_change(language)
    
    def set_language(self, language: Language) -> None:
        """Programmatically set the language."""
        self.current_language = language
        self.update()
    
    def get_current_language(self) -> Optional[Language]:
        """Get the currently selected language."""
        return self.current_language


class LanguageSelectorCard(ft.UserControl):
    """Card-based language selector for onboarding."""
    
    def __init__(
        self,
        on_language_change: Optional[Callable[[Language], None]] = None,
        selected_language: Optional[Language] = None
    ):
        super().__init__()
        self.on_language_change = on_language_change
        self.selected_language = selected_language
        
        # Language display information
        self.language_info = {
            Language.ARABIC: {
                "native_name": "العربية",
                "english_name": "Arabic",
                "flag": "🇲🇦"
            },
            Language.FRENCH: {
                "native_name": "Français", 
                "english_name": "French",
                "flag": "🇫🇷"
            }
        }
    
    def build(self):
        """Build the card-based language selector."""
        cards = []
        
        for language in Language:
            info = self.language_info[language]
            is_selected = self.selected_language == language
            
            card = ft.Card(
                content=ft.Container(
                    content=ft.Column([
                        ft.Text(
                            info["flag"],
                            size=32,
                            text_align=ft.TextAlign.CENTER
                        ),
                        ft.Text(
                            info["native_name"],
                            size=18,
                            weight=ft.FontWeight.BOLD,
                            text_align=ft.TextAlign.CENTER
                        ),
                        ft.Text(
                            info["english_name"],
                            size=14,
                            color=ft.Colors.GREY_600,
                            text_align=ft.TextAlign.CENTER
                        )
                    ],
                    alignment=ft.MainAxisAlignment.CENTER,
                    horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                    spacing=8
                    ),
                    padding=ft.padding.all(20),
                    alignment=ft.alignment.center,
                    bgcolor=ft.Colors.BLUE_100 if is_selected else None,
                    border_radius=12,
                    ink=True,
                    on_click=lambda e, lang=language: self._on_card_click(lang)
                ),
                elevation=2 if not is_selected else 8,
                margin=ft.margin.symmetric(horizontal=8)
            )
            
            cards.append(card)
        
        return ft.Row(
            cards,
            alignment=ft.MainAxisAlignment.CENTER,
            spacing=16
        )
    
    def _on_card_click(self, language: Language) -> None:
        """Handle card click."""
        self.selected_language = language
        self.update()
        
        if self.on_language_change:
            self.on_language_change(language)
    
    def set_selected_language(self, language: Language) -> None:
        """Set the selected language."""
        self.selected_language = language
        self.update()
    
    def get_selected_language(self) -> Optional[Language]:
        """Get the selected language."""
        return self.selected_language
