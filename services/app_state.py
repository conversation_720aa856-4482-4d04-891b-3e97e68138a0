"""
Application state management service for the Hiel Physics application.

This module provides a singleton AppState class that manages global application state,
user preferences, and configuration. It can be accessed from different components
throughout the application to maintain consistent state.
"""

import json
import os
from typing import Dict, Any, Optional, Callable, List
from dataclasses import dataclass, asdict
from enum import Enum
import threading
from pathlib import Path

# Import settings from config module
try:
    from config.settings import (
        Language, EducationalTrack, EducationalLevel, ThemeMode,
        DEFAULT_USER_PREFERENCES, USER_DATA_FILE, SETTINGS_FILE,
        PROGRESS_FILE
    )
except ImportError:
    # Fallback definitions if config module is not available yet
    class Language(Enum):
        ARABIC = "ar"
        FRENCH = "fr"
    
    class EducationalTrack(Enum):
        SCIENCES_MATHS_A = "sciences_maths_a"
        SCIENCES_MATHS_B = "sciences_maths_b"
        SCIENCES_PHYSIQUES = "sciences_physiques"
        SVT = "svt"
        TECHNICAL_INDUSTRIAL = "technical_industrial"
        TECHNICAL_ELECTRICAL = "technical_electrical"
        TECHNICAL_MECHANICAL = "technical_mechanical"
    
    class EducationalLevel(Enum):
        FIRST_YEAR = "1bac"
        SECOND_YEAR = "2bac"
    
    class ThemeMode(Enum):
        LIGHT = "light"
        DARK = "dark"
        SYSTEM = "system"
    
    DEFAULT_USER_PREFERENCES = {
        "language": Language.ARABIC.value,
        "theme_mode": ThemeMode.LIGHT.value,
        "educational_track": None,
        "educational_level": None,
        "navigation_collapsed": False,
        "show_animations": True,
        "auto_save": True,
        "notification_enabled": True,
        "sound_enabled": True
    }
    
    USER_DATA_FILE = "user_data.json"
    SETTINGS_FILE = "app_settings.json"
    PROGRESS_FILE = "user_progress.json"


@dataclass
class UserProfile:
    """User profile information."""
    username: Optional[str] = None
    email: Optional[str] = None
    full_name: Optional[str] = None
    educational_track: Optional[EducationalTrack] = None
    educational_level: Optional[EducationalLevel] = None
    created_at: Optional[str] = None
    last_login: Optional[str] = None


@dataclass
class UserPreferences:
    """User preferences and settings."""
    language: Language = Language.ARABIC
    theme_mode: ThemeMode = ThemeMode.LIGHT
    navigation_collapsed: bool = False
    show_animations: bool = True
    auto_save: bool = True
    notification_enabled: bool = True
    sound_enabled: bool = True
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UserPreferences':
        """Create UserPreferences from dictionary."""
        return cls(
            language=Language(data.get('language', Language.ARABIC.value)),
            theme_mode=ThemeMode(data.get('theme_mode', ThemeMode.LIGHT.value)),
            navigation_collapsed=data.get('navigation_collapsed', False),
            show_animations=data.get('show_animations', True),
            auto_save=data.get('auto_save', True),
            notification_enabled=data.get('notification_enabled', True),
            sound_enabled=data.get('sound_enabled', True)
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert UserPreferences to dictionary."""
        return {
            'language': self.language.value,
            'theme_mode': self.theme_mode.value,
            'navigation_collapsed': self.navigation_collapsed,
            'show_animations': self.show_animations,
            'auto_save': self.auto_save,
            'notification_enabled': self.notification_enabled,
            'sound_enabled': self.sound_enabled
        }


@dataclass
class AppSettings:
    """Application-wide settings."""
    first_run: bool = True
    data_directory: str = "."
    cache_enabled: bool = True
    debug_mode: bool = False
    auto_update: bool = True
    telemetry_enabled: bool = False


class AppState:
    """
    Singleton class for managing global application state.
    
    This class handles user preferences, application settings, user profile,
    and provides methods for state updates and persistence.
    """
    
    _instance: Optional['AppState'] = None
    _lock = threading.Lock()
    
    def __new__(cls) -> 'AppState':
        """Ensure singleton pattern implementation."""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """Initialize the application state."""
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        self._lock = threading.Lock()
        
        # State components
        self.user_profile: Optional[UserProfile] = None
        self.user_preferences: UserPreferences = UserPreferences()
        self.app_settings: AppSettings = AppSettings()
        
        # Application state
        self.is_authenticated: bool = False
        self.current_route: str = "/"
        self.navigation_history: List[str] = []
        self.loading_states: Dict[str, bool] = {}
        self.error_states: Dict[str, Optional[str]] = {}
        
        # Event listeners
        self._state_change_listeners: List[Callable[[str, Any], None]] = []
        self._preference_change_listeners: List[Callable[[str, Any], None]] = []
        
        # Data directory setup
        self.data_dir = Path.home() / ".hiel_physics"
        self.data_dir.mkdir(exist_ok=True)
        
        # Load saved state
        self._load_state()
    
    @classmethod
    def get_instance(cls) -> 'AppState':
        """Get the singleton instance of AppState."""
        return cls()
    
    def add_state_change_listener(self, listener: Callable[[str, Any], None]) -> None:
        """
        Add a listener for state changes.
        
        Args:
            listener: Function to call when state changes (key, value)
        """
        self._state_change_listeners.append(listener)
    
    def remove_state_change_listener(self, listener: Callable[[str, Any], None]) -> None:
        """
        Remove a state change listener.
        
        Args:
            listener: Function to remove from listeners
        """
        if listener in self._state_change_listeners:
            self._state_change_listeners.remove(listener)
    
    def add_preference_change_listener(self, listener: Callable[[str, Any], None]) -> None:
        """
        Add a listener for preference changes.
        
        Args:
            listener: Function to call when preferences change (key, value)
        """
        self._preference_change_listeners.append(listener)
    
    def remove_preference_change_listener(self, listener: Callable[[str, Any], None]) -> None:
        """
        Remove a preference change listener.
        
        Args:
            listener: Function to remove from listeners
        """
        if listener in self._preference_change_listeners:
            self._preference_change_listeners.remove(listener)
    
    def _notify_state_change(self, key: str, value: Any) -> None:
        """Notify all listeners of state changes."""
        for listener in self._state_change_listeners:
            try:
                listener(key, value)
            except Exception as e:
                print(f"Error in state change listener: {e}")
    
    def _notify_preference_change(self, key: str, value: Any) -> None:
        """Notify all listeners of preference changes."""
        for listener in self._preference_change_listeners:
            try:
                listener(key, value)
            except Exception as e:
                print(f"Error in preference change listener: {e}")
    
    # User Profile Management
    def set_user_profile(self, profile: UserProfile) -> None:
        """
        Set the user profile.
        
        Args:
            profile: UserProfile object
        """
        with self._lock:
            self.user_profile = profile
            self._notify_state_change("user_profile", profile)
            self._save_user_data()
    
    def get_user_profile(self) -> Optional[UserProfile]:
        """Get the current user profile."""
        return self.user_profile
    
    def update_user_profile(self, **kwargs) -> None:
        """
        Update specific fields in the user profile.
        
        Args:
            **kwargs: Fields to update in the user profile
        """
        if self.user_profile is None:
            self.user_profile = UserProfile()
        
        with self._lock:
            for key, value in kwargs.items():
                if hasattr(self.user_profile, key):
                    setattr(self.user_profile, key, value)
            
            self._notify_state_change("user_profile", self.user_profile)
            self._save_user_data()
    
    # User Preferences Management
    def set_language(self, language: Language) -> None:
        """
        Set the application language.
        
        Args:
            language: Language enum value
        """
        with self._lock:
            self.user_preferences.language = language
            self._notify_preference_change("language", language)
            self._save_preferences()
    
    def get_language(self) -> Language:
        """Get the current application language."""
        return self.user_preferences.language
    
    def set_theme_mode(self, theme_mode: ThemeMode) -> None:
        """
        Set the application theme mode.
        
        Args:
            theme_mode: ThemeMode enum value
        """
        with self._lock:
            self.user_preferences.theme_mode = theme_mode
            self._notify_preference_change("theme_mode", theme_mode)
            self._save_preferences()
    
    def get_theme_mode(self) -> ThemeMode:
        """Get the current theme mode."""
        return self.user_preferences.theme_mode

    def set_navigation_collapsed(self, collapsed: bool) -> None:
        """
        Set the navigation collapsed state.

        Args:
            collapsed: Whether navigation should be collapsed
        """
        with self._lock:
            self.user_preferences.navigation_collapsed = collapsed
            self._notify_preference_change("navigation_collapsed", collapsed)
            self._save_preferences()

    def get_navigation_collapsed(self) -> bool:
        """Get the navigation collapsed state."""
        return self.user_preferences.navigation_collapsed

    def set_educational_track(self, track: Optional[EducationalTrack]) -> None:
        """
        Set the user's educational track.
        
        Args:
            track: EducationalTrack enum value or None
        """
        if self.user_profile is None:
            self.user_profile = UserProfile()
        
        with self._lock:
            self.user_profile.educational_track = track
            self._notify_state_change("educational_track", track)
            self._save_user_data()
    
    def get_educational_track(self) -> Optional[EducationalTrack]:
        """Get the user's educational track."""
        return self.user_profile.educational_track if self.user_profile else None
    
    def set_educational_level(self, level: Optional[EducationalLevel]) -> None:
        """
        Set the user's educational level.
        
        Args:
            level: EducationalLevel enum value or None
        """
        if self.user_profile is None:
            self.user_profile = UserProfile()
        
        with self._lock:
            self.user_profile.educational_level = level
            self._notify_state_change("educational_level", level)
            self._save_user_data()
    
    def get_educational_level(self) -> Optional[EducationalLevel]:
        """Get the user's educational level."""
        return self.user_profile.educational_level if self.user_profile else None

    def set_first_run(self, first_run: bool) -> None:
        """
        Set the first run flag.

        Args:
            first_run: Whether this is the first run
        """
        with self._lock:
            self.app_settings.first_run = first_run
            self._notify_state_change("first_run", first_run)
            self._save_app_settings()

    def is_first_run(self) -> bool:
        """Check if this is the first run."""
        return self.app_settings.first_run
    
    def update_preference(self, key: str, value: Any) -> None:
        """
        Update a specific preference.
        
        Args:
            key: Preference key
            value: New value
        """
        if hasattr(self.user_preferences, key):
            with self._lock:
                setattr(self.user_preferences, key, value)
                self._notify_preference_change(key, value)
                self._save_preferences()
    
    def get_preference(self, key: str, default: Any = None) -> Any:
        """
        Get a specific preference value.
        
        Args:
            key: Preference key
            default: Default value if key not found
            
        Returns:
            Preference value or default
        """
        return getattr(self.user_preferences, key, default)
    
    # Authentication State
    def set_authenticated(self, authenticated: bool) -> None:
        """
        Set the authentication state.
        
        Args:
            authenticated: Whether user is authenticated
        """
        with self._lock:
            self.is_authenticated = authenticated
            self._notify_state_change("is_authenticated", authenticated)
    
    def is_user_authenticated(self) -> bool:
        """Check if user is authenticated."""
        return self.is_authenticated
    
    # Navigation State
    def set_current_route(self, route: str) -> None:
        """
        Set the current route.
        
        Args:
            route: Current route path
        """
        with self._lock:
            if self.current_route != route:
                self.navigation_history.append(self.current_route)
                self.current_route = route
                self._notify_state_change("current_route", route)
    
    def get_current_route(self) -> str:
        """Get the current route."""
        return self.current_route
    
    def get_navigation_history(self) -> List[str]:
        """Get the navigation history."""
        return self.navigation_history.copy()
    
    def clear_navigation_history(self) -> None:
        """Clear the navigation history."""
        with self._lock:
            self.navigation_history.clear()
            self._notify_state_change("navigation_history", [])
    
    # Loading States
    def set_loading(self, key: str, loading: bool) -> None:
        """
        Set loading state for a specific operation.
        
        Args:
            key: Loading operation key
            loading: Whether operation is loading
        """
        with self._lock:
            self.loading_states[key] = loading
            self._notify_state_change(f"loading_{key}", loading)
    
    def is_loading(self, key: str) -> bool:
        """
        Check if a specific operation is loading.
        
        Args:
            key: Loading operation key
            
        Returns:
            True if loading, False otherwise
        """
        return self.loading_states.get(key, False)
    
    def clear_loading_states(self) -> None:
        """Clear all loading states."""
        with self._lock:
            self.loading_states.clear()
            self._notify_state_change("loading_states", {})
    
    # Error States
    def set_error(self, key: str, error: Optional[str]) -> None:
        """
        Set error state for a specific operation.
        
        Args:
            key: Error operation key
            error: Error message or None to clear
        """
        with self._lock:
            self.error_states[key] = error
            self._notify_state_change(f"error_{key}", error)
    
    def get_error(self, key: str) -> Optional[str]:
        """
        Get error message for a specific operation.
        
        Args:
            key: Error operation key
            
        Returns:
            Error message or None
        """
        return self.error_states.get(key)
    
    def clear_error(self, key: str) -> None:
        """
        Clear error state for a specific operation.
        
        Args:
            key: Error operation key
        """
        self.set_error(key, None)
    
    def clear_all_errors(self) -> None:
        """Clear all error states."""
        with self._lock:
            self.error_states.clear()
            self._notify_state_change("error_states", {})
    
    # State Persistence
    def _load_state(self) -> None:
        """Load state from persistent storage."""
        try:
            # Load user preferences
            preferences_file = self.data_dir / SETTINGS_FILE
            if preferences_file.exists():
                with open(preferences_file, 'r', encoding='utf-8') as f:
                    prefs_data = json.load(f)
                    self.user_preferences = UserPreferences.from_dict(prefs_data)
            
            # Load user profile
            user_data_file = self.data_dir / USER_DATA_FILE
            if user_data_file.exists():
                with open(user_data_file, 'r', encoding='utf-8') as f:
                    user_data = json.load(f)
                    if user_data:
                        self.user_profile = UserProfile(**user_data)

            # Load app settings
            app_settings_file = self.data_dir / "app_settings.json"
            if app_settings_file.exists():
                with open(app_settings_file, 'r', encoding='utf-8') as f:
                    settings_data = json.load(f)
                    self.app_settings = AppSettings(**settings_data)

        except Exception as e:
            print(f"Error loading state: {e}")
            # Reset to defaults on error
            self.user_preferences = UserPreferences()
            self.user_profile = None
            self.app_settings = AppSettings()
    
    def _save_preferences(self) -> None:
        """Save user preferences to persistent storage."""
        try:
            preferences_file = self.data_dir / SETTINGS_FILE
            with open(preferences_file, 'w', encoding='utf-8') as f:
                json.dump(self.user_preferences.to_dict(), f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Error saving preferences: {e}")
    
    def _save_user_data(self) -> None:
        """Save user profile to persistent storage."""
        try:
            user_data_file = self.data_dir / USER_DATA_FILE
            user_data = asdict(self.user_profile) if self.user_profile else {}
            with open(user_data_file, 'w', encoding='utf-8') as f:
                json.dump(user_data, f, indent=2, ensure_ascii=False, default=str)
        except Exception as e:
            print(f"Error saving user data: {e}")

    def _save_app_settings(self) -> None:
        """Save app settings to persistent storage."""
        try:
            settings_file = self.data_dir / "app_settings.json"
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(asdict(self.app_settings), f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Error saving app settings: {e}")
    
    def save_state(self) -> None:
        """Manually save all state to persistent storage."""
        self._save_preferences()
        self._save_user_data()
        self._save_app_settings()
    
    def reset_state(self) -> None:
        """Reset application state to defaults."""
        with self._lock:
            self.user_profile = None
            self.user_preferences = UserPreferences()
            self.is_authenticated = False
            self.current_route = "/"
            self.navigation_history.clear()
            self.loading_states.clear()
            self.error_states.clear()
            
            # Notify listeners
            self._notify_state_change("state_reset", True)
            
            # Save reset state
            self.save_state()
    
    def get_state_summary(self) -> Dict[str, Any]:
        """
        Get a summary of the current application state.
        
        Returns:
            Dictionary containing state summary
        """
        return {
            "user_authenticated": self.is_authenticated,
            "current_route": self.current_route,
            "language": self.user_preferences.language.value,
            "theme_mode": self.user_preferences.theme_mode.value,
            "educational_track": self.get_educational_track().value if self.get_educational_track() else None,
            "educational_level": self.get_educational_level().value if self.get_educational_level() else None,
            "loading_operations": list(self.loading_states.keys()),
            "error_operations": [k for k, v in self.error_states.items() if v is not None],
            "navigation_history_length": len(self.navigation_history)
        }


# Convenience functions for accessing the singleton instance
def get_app_state() -> AppState:
    """Get the global application state instance."""
    return AppState.get_instance()


def get_user_preferences() -> UserPreferences:
    """Get the current user preferences."""
    return get_app_state().user_preferences


def get_user_profile() -> Optional[UserProfile]:
    """Get the current user profile."""
    return get_app_state().get_user_profile()


def set_language(language: Language) -> None:
    """Set the application language."""
    get_app_state().set_language(language)


def set_theme_mode(theme_mode: ThemeMode) -> None:
    """Set the application theme mode."""
    get_app_state().set_theme_mode(theme_mode)


def is_authenticated() -> bool:
    """Check if user is authenticated."""
    return get_app_state().is_user_authenticated()


def get_current_language() -> Language:
    """Get the current application language."""
    return get_app_state().get_language()


def get_current_theme() -> ThemeMode:
    """Get the current theme mode."""
    return get_app_state().get_theme_mode()