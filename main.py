"""
Main application entry point for Hiel Physics.

This module initializes the Flet application with proper configuration,
routing capabilities, and error handling for the physics education app.
"""

import flet as ft
import sys
import traceback
from typing import Optional

# Import the router system
from ui.router import create_app_router, Router
# Import services
from services.app_state import AppState
from services.i18n import get_i18n_service
from services.theme_service import ThemeService


class HielPhysicsApp:
    """
    Main application class for Hiel Physics.
    
    Handles app initialization, configuration, and lifecycle management.
    """
    
    def __init__(self):
        """Initialize the application."""
        self.router: Optional[Router] = None
        self.page: Optional[ft.Page] = None
        self.app_state = AppState()
        self.i18n = get_i18n_service()
        self.theme_service: Optional[ThemeService] = None
    
    def initialize_app(self, page: ft.Page) -> None:
        """
        Initialize the application with the given page.

        Args:
            page: The main Flet page object
        """
        try:
            self.page = page

            # Initialize theme service
            self.theme_service = ThemeService(self.app_state)

            # Configure page properties
            self._configure_page()

            # Initialize i18n service with user's preferred language
            current_language = self.app_state.get_language()
            if current_language:
                self.i18n.set_language(current_language)
                # Set RTL for Arabic
                page.rtl = self.i18n.is_rtl()

            # Initialize router
            self.router = create_app_router(page, self.i18n, self.theme_service)

            # Set initial route based on onboarding status
            if not page.route or page.route == "/":
                if self.app_state.app_settings.first_run or not self.app_state.get_educational_track():
                    page.go("/onboarding/language")
                else:
                    page.go("/home")

            print("Hiel Physics app initialized successfully")

        except Exception as e:
            self._handle_initialization_error(e)
    
    def _configure_page(self) -> None:
        """Configure the main page properties."""
        if not self.page:
            return
        
        # Basic page configuration
        self.page.title = "Hiel Physics - فيزياء هيل"
        self.page.theme_mode = ft.ThemeMode.SYSTEM
        self.page.padding = 0
        self.page.spacing = 0
        
        # Window properties
        self.page.window_width = 1200
        self.page.window_height = 800
        self.page.window_min_width = 800
        self.page.window_min_height = 600
        self.page.window_resizable = True
        self.page.window_maximizable = True
        
        # App bar configuration
        self.page.appbar = None  # Will be handled by individual views
        
        # Theme configuration
        self._configure_theme()

        # Set up page resize handler for responsive design
        self.page.on_resize = self._handle_page_resize

        # Error handling
        self.page.on_error = self._handle_page_error

        # Keyboard shortcuts
        self.page.on_keyboard_event = self._handle_keyboard_event
    
    def _configure_theme(self) -> None:
        """Configure the application theme."""
        if not self.page or not self.theme_service:
            return

        # Apply theme from theme service
        current_theme = self.theme_service.get_current_theme()
        self.page.theme = current_theme

        # Set theme mode based on user preference
        theme_mode = self.theme_service.get_current_theme_mode()
        if theme_mode.value == "light":
            self.page.theme_mode = ft.ThemeMode.LIGHT
        elif theme_mode.value == "dark":
            self.page.theme_mode = ft.ThemeMode.DARK
        else:
            self.page.theme_mode = ft.ThemeMode.SYSTEM

        # Listen for theme changes
        self.theme_service.add_theme_change_listener(self._on_theme_change)
    
    def _handle_initialization_error(self, error: Exception) -> None:
        """
        Handle errors during app initialization.
        
        Args:
            error: The exception that occurred
        """
        error_message = f"Failed to initialize Hiel Physics app: {str(error)}"
        print(error_message)
        traceback.print_exc()
        
        if self.page:
            # Show error dialog
            self.page.dialog = ft.AlertDialog(
                title=ft.Text("Initialization Error"),
                content=ft.Text(error_message),
                actions=[
                    ft.TextButton(
                        "Retry",
                        on_click=lambda _: self._retry_initialization(),
                    ),
                    ft.TextButton(
                        "Exit",
                        on_click=lambda _: self.page.window_close(),
                    ),
                ],
            )
            self.page.dialog.open = True
            self.page.update()
    
    def _retry_initialization(self) -> None:
        """Retry application initialization."""
        if self.page:
            self.page.dialog.open = False
            self.page.update()
            self.initialize_app(self.page)
    
    def _handle_page_error(self, e) -> None:
        """
        Handle page-level errors.
        
        Args:
            e: Error event
        """
        error_message = f"Page error: {str(e.data)}"
        print(error_message)
        
        if self.page:
            # Show error snackbar
            self.page.snack_bar = ft.SnackBar(
                content=ft.Text("An error occurred. Please try again."),
                action="Dismiss",
                action_color=ft.Colors.BLUE,
            )
            self.page.snack_bar.open = True
            self.page.update()
    
    def _handle_keyboard_event(self, e: ft.KeyboardEvent) -> None:
        """
        Handle keyboard shortcuts.
        
        Args:
            e: Keyboard event
        """
        # Handle common keyboard shortcuts
        if e.key == "F5":
            # Refresh current page
            if self.router and self.page:
                current_route = self.page.route
                self.page.go(current_route)
        
        elif e.key == "Escape":
            # Go back or close dialogs
            if self.page and self.page.dialog and self.page.dialog.open:
                self.page.dialog.open = False
                self.page.update()
            elif self.router and self.router.can_go_back():
                self.router.go_back()
        
        elif e.ctrl and e.key == "h":
            # Go to home
            if self.router:
                self.router.navigate_to("/home")

    def _handle_page_resize(self, e) -> None:
        """Handle page resize events for responsive design.

        Args:
            e: Resize event
        """
        # The main layout will handle responsive changes automatically
        pass

    def _on_theme_change(self) -> None:
        """Handle theme changes from theme service."""
        if self.page and self.theme_service:
            current_theme = self.theme_service.get_current_theme()
            self.page.theme = current_theme

            # Update theme mode
            theme_mode = self.theme_service.get_current_theme_mode()
            if theme_mode.value == "light":
                self.page.theme_mode = ft.ThemeMode.LIGHT
            elif theme_mode.value == "dark":
                self.page.theme_mode = ft.ThemeMode.DARK
            else:
                self.page.theme_mode = ft.ThemeMode.SYSTEM

            self.page.update()


def main(page: ft.Page) -> None:
    """
    Main function that initializes and runs the Hiel Physics application.
    
    Args:
        page: The main Flet page object
    """
    # Create and initialize the app
    app = HielPhysicsApp()
    app.initialize_app(page)


def run_app() -> None:
    """
    Run the Hiel Physics application.
    
    This function can be called from command line or other entry points.
    """
    try:
        # Configure Flet app settings
        ft.app(
            target=main,
            name="Hiel Physics",
            assets_dir="assets",
            upload_dir="uploads",
            web_renderer=ft.WebRenderer.HTML,
        )
    except Exception as e:
        print(f"Failed to start Hiel Physics app: {e}")
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    """Entry point when running the script directly."""
    print("Starting Hiel Physics - Physics Education App")
    print("فيزياء هيل - تطبيق تعليم الفيزياء")
    print("=" * 50)
    
    try:
        run_app()
    except KeyboardInterrupt:
        print("\nApplication interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"Unexpected error: {e}")
        traceback.print_exc()
        sys.exit(1)